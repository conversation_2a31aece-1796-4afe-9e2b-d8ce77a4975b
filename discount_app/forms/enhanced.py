"""
Enhanced forms for CozyWish discount system with Pydantic validation integration.
These forms provide advanced validation using the new business rules engine.
"""

import json
from decimal import Decimal
from datetime import datetime
from typing import Dict, Any, List, Optional

from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model

from venues_app.models import Category, Venue, Service
from ..models import DiscountType, PlatformDiscount, ServiceDiscount, VenueDiscount
from ..models.coupon_models import CouponCode, CouponType, CouponStatus, DiscountTarget
from ..schemas import (
    ComprehensiveDiscountRulesSchema,
    CouponValidationSchema,
    UserEligibilitySchema,
    BookingEligibilitySchema,
    TimeRestrictionSchema,
    UsageLimitSchema,
    CombinationRulesSchema,
    CouponGenerationSchema,
    PromotionCampaignSchema,
    UserTypeEnum,
    CouponTypeEnum,
)
from ..business_rules import business_rules_engine, DiscountContext

User = get_user_model()


class BusinessRulesFormMixin:
    """Mixin for forms that need to validate business rules."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._add_business_rules_fields()

    def _add_business_rules_fields(self):
        """Add business rules fields to the form."""
        # User eligibility fields
        self.fields['new_users_only'] = forms.BooleanField(
            required=False,
            help_text=_("Only allow new users to use this discount"),
            widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
        )
        
        self.fields['user_types'] = forms.MultipleChoiceField(
            choices=[(ut.value, ut.value.replace('_', ' ').title()) for ut in UserTypeEnum],
            required=False,
            help_text=_("Select user types eligible for this discount"),
            widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input'})
        )
        
        self.fields['min_account_age_days'] = forms.IntegerField(
            required=False,
            min_value=0,
            help_text=_("Minimum account age in days"),
            widget=forms.NumberInput(attrs={'class': 'form-control'})
        )
        
        self.fields['max_account_age_days'] = forms.IntegerField(
            required=False,
            min_value=0,
            help_text=_("Maximum account age in days"),
            widget=forms.NumberInput(attrs={'class': 'form-control'})
        )
        
        # Booking eligibility fields
        self.fields['min_booking_value'] = forms.DecimalField(
            required=False,
            min_value=Decimal('0'),
            decimal_places=2,
            help_text=_("Minimum booking value required"),
            widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'})
        )
        
        self.fields['max_booking_value'] = forms.DecimalField(
            required=False,
            min_value=Decimal('0'),
            decimal_places=2,
            help_text=_("Maximum booking value allowed"),
            widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'})
        )
        
        self.fields['min_items'] = forms.IntegerField(
            required=False,
            min_value=1,
            help_text=_("Minimum number of items required"),
            widget=forms.NumberInput(attrs={'class': 'form-control'})
        )
        
        self.fields['max_items'] = forms.IntegerField(
            required=False,
            min_value=1,
            help_text=_("Maximum number of items allowed"),
            widget=forms.NumberInput(attrs={'class': 'form-control'})
        )
        
        self.fields['allowed_categories'] = forms.ModelMultipleChoiceField(
            queryset=Category.objects.filter(is_active=True),
            required=False,
            help_text=_("Categories this discount applies to"),
            widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input'})
        )
        
        self.fields['excluded_categories'] = forms.ModelMultipleChoiceField(
            queryset=Category.objects.filter(is_active=True),
            required=False,
            help_text=_("Categories this discount does NOT apply to"),
            widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input'})
        )
        
        # Usage limits
        self.fields['max_uses_per_user'] = forms.IntegerField(
            required=False,
            min_value=1,
            help_text=_("Maximum uses per user"),
            widget=forms.NumberInput(attrs={'class': 'form-control'})
        )
        
        self.fields['max_total_uses'] = forms.IntegerField(
            required=False,
            min_value=1,
            help_text=_("Maximum total uses across all users"),
            widget=forms.NumberInput(attrs={'class': 'form-control'})
        )
        
        self.fields['cooldown_period_hours'] = forms.IntegerField(
            required=False,
            min_value=1,
            help_text=_("Hours between uses by the same user"),
            widget=forms.NumberInput(attrs={'class': 'form-control'})
        )
        
        # Combination rules
        self.fields['can_combine_with_coupons'] = forms.BooleanField(
            required=False,
            initial=True,
            help_text=_("Allow combining with coupon codes"),
            widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
        )
        
        self.fields['can_combine_with_discounts'] = forms.BooleanField(
            required=False,
            initial=False,
            help_text=_("Allow combining with other discounts"),
            widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
        )
        
        self.fields['max_total_discount_percentage'] = forms.DecimalField(
            required=False,
            min_value=Decimal('0'),
            max_value=Decimal('100'),
            decimal_places=2,
            help_text=_("Maximum total discount percentage when combined"),
            widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'})
        )
        
        # Advanced features
        self.fields['requires_approval'] = forms.BooleanField(
            required=False,
            help_text=_("Require admin approval before use"),
            widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
        )
        
        self.fields['auto_apply'] = forms.BooleanField(
            required=False,
            help_text=_("Automatically apply if eligible"),
            widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
        )
        
        self.fields['show_in_listings'] = forms.BooleanField(
            required=False,
            initial=True,
            help_text=_("Show in public discount listings"),
            widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
        )

    def clean(self):
        """Enhanced validation using business rules."""
        cleaned_data = super().clean()
        
        # Build business rules from form data
        business_rules = self._build_business_rules_from_form(cleaned_data)
        
        # Validate business rules using Pydantic
        try:
            ComprehensiveDiscountRulesSchema(**business_rules)
        except Exception as e:
            raise ValidationError(f"Business rules validation failed: {str(e)}")
        
        return cleaned_data

    def _build_business_rules_from_form(self, cleaned_data: Dict[str, Any]) -> Dict[str, Any]:
        """Build business rules dictionary from form data."""
        rules = {
            'user_eligibility': {
                'new_users_only': cleaned_data.get('new_users_only', False),
                'user_types': cleaned_data.get('user_types', []),
                'min_account_age_days': cleaned_data.get('min_account_age_days'),
                'max_account_age_days': cleaned_data.get('max_account_age_days'),
            },
            'booking_eligibility': {
                'min_booking_value': cleaned_data.get('min_booking_value'),
                'max_booking_value': cleaned_data.get('max_booking_value'),
                'min_items': cleaned_data.get('min_items'),
                'max_items': cleaned_data.get('max_items'),
                'allowed_categories': [cat.id for cat in cleaned_data.get('allowed_categories', [])],
                'excluded_categories': [cat.id for cat in cleaned_data.get('excluded_categories', [])],
            },
            'usage_limits': {
                'max_uses_per_user': cleaned_data.get('max_uses_per_user'),
                'max_total_uses': cleaned_data.get('max_total_uses'),
                'cooldown_period_hours': cleaned_data.get('cooldown_period_hours'),
            },
            'combination_rules': {
                'can_combine_with_coupons': cleaned_data.get('can_combine_with_coupons', True),
                'can_combine_with_discounts': cleaned_data.get('can_combine_with_discounts', False),
                'max_total_discount_percentage': cleaned_data.get('max_total_discount_percentage'),
            },
            'requires_approval': cleaned_data.get('requires_approval', False),
            'auto_apply': cleaned_data.get('auto_apply', False),
            'show_in_listings': cleaned_data.get('show_in_listings', True),
        }
        
        # Remove None values
        return {k: v for k, v in rules.items() if v is not None}

    def save(self, commit=True):
        """Save the form with business rules."""
        instance = super().save(commit=False)
        
        # Build and save business rules
        business_rules = self._build_business_rules_from_form(self.cleaned_data)
        instance.business_rules = business_rules
        
        if commit:
            instance.save()
            
        return instance


class EnhancedPlatformDiscountForm(BusinessRulesFormMixin, forms.ModelForm):
    """Enhanced form for platform discounts with business rules integration."""

    class Meta:
        model = PlatformDiscount
        fields = [
            'name', 'description', 'discount_type', 'discount_value',
            'start_date', 'end_date', 'category', 'is_featured',
            'is_stackable', 'priority'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'discount_type': forms.Select(attrs={'class': 'form-select'}),
            'discount_value': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'start_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'end_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'category': forms.Select(attrs={'class': 'form-select'}),
            'is_featured': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_stackable': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'priority': forms.NumberInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['category'].queryset = Category.objects.filter(is_active=True)
        self.fields['category'].empty_label = "All Categories"
        self.fields['category'].required = False


class CouponCodeForm(forms.ModelForm):
    """Form for creating and editing coupon codes."""

    class Meta:
        model = CouponCode
        fields = [
            'code', 'name', 'description', 'coupon_type', 'status', 'target',
            'discount_value', 'valid_from', 'valid_until', 'usage_limit',
            'usage_limit_per_user', 'is_stackable', 'priority'
        ]
        widgets = {
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Leave blank to auto-generate'}),
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'coupon_type': forms.Select(attrs={'class': 'form-select'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'target': forms.Select(attrs={'class': 'form-select'}),
            'discount_value': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'valid_from': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'valid_until': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'usage_limit': forms.NumberInput(attrs={'class': 'form-control'}),
            'usage_limit_per_user': forms.NumberInput(attrs={'class': 'form-control'}),
            'is_stackable': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'priority': forms.NumberInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['code'].required = False
        self.fields['usage_limit'].required = False

    def clean(self):
        """Validate coupon code form."""
        cleaned_data = super().clean()
        
        valid_from = cleaned_data.get('valid_from')
        valid_until = cleaned_data.get('valid_until')
        
        if valid_from and valid_until:
            if valid_from >= valid_until:
                raise ValidationError(_("Valid from date must be before valid until date"))
                
        return cleaned_data


class CouponGenerationForm(forms.Form):
    """Form for generating multiple coupon codes."""

    prefix = forms.CharField(
        max_length=10,
        required=False,
        initial="COZY",
        help_text=_("Prefix for generated codes"),
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    
    length = forms.IntegerField(
        min_value=4,
        max_value=20,
        initial=8,
        help_text=_("Length of generated codes"),
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )
    
    quantity = forms.IntegerField(
        min_value=1,
        max_value=1000,
        initial=10,
        help_text=_("Number of codes to generate"),
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )
    
    exclude_ambiguous = forms.BooleanField(
        required=False,
        initial=True,
        help_text=_("Exclude ambiguous characters (0, O, 1, I)"),
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    
    # Coupon details
    name_template = forms.CharField(
        max_length=255,
        help_text=_("Template for coupon names (use {code} for the generated code)"),
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    
    description = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        help_text=_("Description for all generated coupons")
    )
    
    coupon_type = forms.ChoiceField(
        choices=CouponType.choices,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    discount_value = forms.DecimalField(
        min_value=Decimal('0.01'),
        decimal_places=2,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'})
    )
    
    valid_from = forms.DateTimeField(
        widget=forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'})
    )
    
    valid_until = forms.DateTimeField(
        widget=forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'})
    )
    
    usage_limit = forms.IntegerField(
        required=False,
        min_value=1,
        help_text=_("Maximum uses per coupon (blank for unlimited)"),
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )
    
    usage_limit_per_user = forms.IntegerField(
        min_value=1,
        initial=1,
        help_text=_("Maximum uses per user per coupon"),
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )

    def clean(self):
        """Validate coupon generation form."""
        cleaned_data = super().clean()
        
        # Validate generation parameters using Pydantic
        try:
            generation_params = {
                'prefix': cleaned_data.get('prefix'),
                'length': cleaned_data.get('length'),
                'quantity': cleaned_data.get('quantity'),
                'exclude_ambiguous': cleaned_data.get('exclude_ambiguous', True),
            }
            CouponGenerationSchema(**generation_params)
        except Exception as e:
            raise ValidationError(f"Generation parameters validation failed: {str(e)}")
        
        # Validate date range
        valid_from = cleaned_data.get('valid_from')
        valid_until = cleaned_data.get('valid_until')
        
        if valid_from and valid_until:
            if valid_from >= valid_until:
                raise ValidationError(_("Valid from date must be before valid until date"))
                
        return cleaned_data


class CouponValidationForm(forms.Form):
    """Form for validating coupon codes."""

    code = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter coupon code'
        })
    )
    
    booking_total = forms.DecimalField(
        required=False,
        min_value=Decimal('0'),
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.01',
            'placeholder': 'Booking total (optional)'
        })
    )

    def clean_code(self):
        """Clean and validate coupon code."""
        code = self.cleaned_data.get('code', '').strip().upper()
        
        if not code:
            raise ValidationError(_("Coupon code is required"))
            
        # Use Pydantic for validation
        try:
            validation_data = {
                'code': code,
                'booking_total': self.cleaned_data.get('booking_total'),
            }
            CouponValidationSchema(**validation_data)
        except Exception as e:
            raise ValidationError(f"Coupon validation failed: {str(e)}")
            
        return code


class DiscountEligibilityTestForm(forms.Form):
    """Form for testing discount eligibility against business rules."""

    discount_id = forms.IntegerField(
        widget=forms.HiddenInput()
    )
    
    user_id = forms.ModelChoiceField(
        queryset=User.objects.all(),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    booking_total = forms.DecimalField(
        min_value=Decimal('0'),
        decimal_places=2,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'})
    )
    
    category_ids = forms.ModelMultipleChoiceField(
        queryset=Category.objects.filter(is_active=True),
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input'})
    )
    
    venue_ids = forms.ModelMultipleChoiceField(
        queryset=Venue.objects.filter(is_active=True),
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input'})
    )
    
    item_count = forms.IntegerField(
        min_value=1,
        initial=1,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )

    def test_eligibility(self):
        """Test discount eligibility using the business rules engine."""
        if not self.is_valid():
            return None
            
        # Get the discount object
        discount_model = None
        discount_id = self.cleaned_data['discount_id']
        
        # Try to find the discount in different models
        for model in [PlatformDiscount, VenueDiscount, ServiceDiscount]:
            try:
                discount_model = model.objects.get(id=discount_id)
                break
            except model.DoesNotExist:
                continue
        
        if not discount_model:
            return {'error': 'Discount not found'}
        
        # Create discount context
        context = DiscountContext(
            user=self.cleaned_data.get('user_id'),
            booking_total=self.cleaned_data['booking_total'],
            category_ids=[cat.id for cat in self.cleaned_data.get('category_ids', [])],
            venue_ids=[venue.id for venue in self.cleaned_data.get('venue_ids', [])],
            item_count=self.cleaned_data.get('item_count', 1),
            booking_date=timezone.now()
        )
        
        # Test eligibility
        result = business_rules_engine.calculate_discount(discount_model, context)
        
        return {
            'is_eligible': result.is_eligible,
            'discount_amount': result.discount_amount,
            'final_amount': result.final_amount,
            'applied_rules': result.applied_rules,
            'error_messages': result.error_messages,
            'warnings': result.warnings,
        } 