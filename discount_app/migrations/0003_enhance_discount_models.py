# Generated manually for discount app enhancements - Part 4 Modernization

import django.core.validators
from decimal import Decimal
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
import djmoney.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('discount_app', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        # Add new fields to existing discount models
        migrations.AddField(
            model_name='discountusage',
            name='ip_address',
            field=models.GenericIPAddressField(blank=True, help_text='IP address of the user', null=True),
        ),
        migrations.AddField(
            model_name='platformdiscount',
            name='business_rules',
            field=models.JSONField(default=dict, help_text='Advanced business rules for discount application'),
        ),
        migrations.AddField(
            model_name='platformdiscount',
            name='current_usage_count',
            field=models.PositiveIntegerField(default=0, help_text='Current number of times this discount has been used'),
        ),
        migrations.AddField(
            model_name='platformdiscount',
            name='is_stackable',
            field=models.BooleanField(default=False, help_text='Can this discount be combined with coupon codes?'),
        ),
        migrations.AddField(
            model_name='platformdiscount',
            name='priority',
            field=models.PositiveIntegerField(default=10, help_text='Priority for discount application (higher number = higher priority)'),
        ),
        migrations.AddField(
            model_name='servicediscount',
            name='business_rules',
            field=models.JSONField(default=dict, help_text='Advanced business rules for discount application'),
        ),
        migrations.AddField(
            model_name='servicediscount',
            name='current_usage_count',
            field=models.PositiveIntegerField(default=0, help_text='Current number of times this discount has been used'),
        ),
        migrations.AddField(
            model_name='servicediscount',
            name='is_stackable',
            field=models.BooleanField(default=False, help_text='Can this discount be combined with coupon codes?'),
        ),
        migrations.AddField(
            model_name='servicediscount',
            name='priority',
            field=models.PositiveIntegerField(default=10, help_text='Priority for discount application (higher number = higher priority)'),
        ),
        migrations.AddField(
            model_name='venuediscount',
            name='business_rules',
            field=models.JSONField(default=dict, help_text='Advanced business rules for discount application'),
        ),
        migrations.AddField(
            model_name='venuediscount',
            name='current_usage_count',
            field=models.PositiveIntegerField(default=0, help_text='Current number of times this discount has been used'),
        ),
        migrations.AddField(
            model_name='venuediscount',
            name='is_stackable',
            field=models.BooleanField(default=False, help_text='Can this discount be combined with coupon codes?'),
        ),
        migrations.AddField(
            model_name='venuediscount',
            name='priority',
            field=models.PositiveIntegerField(default=10, help_text='Priority for discount application (higher number = higher priority)'),
        ),
        
        # Update discount usage model choices
        migrations.AlterField(
            model_name='discountusage',
            name='discount_type',
            field=models.CharField(
                choices=[
                    ('VenueDiscount', 'Venue Discount'),
                    ('ServiceDiscount', 'Service Discount'),
                    ('PlatformDiscount', 'Platform Discount'),
                    ('CouponCode', 'Coupon Code'),
                ],
                help_text='Type of discount that was used',
                max_length=20,
            ),
        ),
        
        # Add constraints to existing models
        migrations.AddConstraint(
            model_name='discountusage',
            constraint=models.CheckConstraint(
                check=models.Q(original_price__gte=0),
                name='positive_original_price',
            ),
        ),
        migrations.AddConstraint(
            model_name='discountusage',
            constraint=models.CheckConstraint(
                check=models.Q(discount_amount__gte=0),
                name='positive_discount_amount',
            ),
        ),
        migrations.AddConstraint(
            model_name='discountusage',
            constraint=models.CheckConstraint(
                check=models.Q(final_price__gte=0),
                name='positive_final_price',
            ),
        ),
        
        # Create new CouponCode model
        migrations.CreateModel(
            name='CouponCode',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('code', models.CharField(help_text='Unique coupon code (auto-generated if not provided)', max_length=50, unique=True)),
                ('name', models.CharField(help_text='Internal name for the coupon', max_length=255)),
                ('description', models.TextField(blank=True, help_text='Description of what this coupon offers')),
                ('coupon_type', models.CharField(
                    choices=[
                        ('percentage', 'Percentage Discount'),
                        ('fixed_amount', 'Fixed Amount Discount'),
                        ('free_shipping', 'Free Shipping'),
                        ('buy_x_get_y', 'Buy X Get Y Free'),
                        ('first_time_user', 'First Time User Discount'),
                    ],
                    default='percentage',
                    help_text='Type of discount this coupon provides',
                    max_length=20,
                )),
                ('status', models.CharField(
                    choices=[
                        ('draft', 'Draft'),
                        ('active', 'Active'),
                        ('paused', 'Paused'),
                        ('expired', 'Expired'),
                        ('depleted', 'Depleted'),
                    ],
                    default='draft',
                    help_text='Current status of the coupon',
                    max_length=20,
                )),
                ('target', models.CharField(
                    choices=[
                        ('order_total', 'Order Total'),
                        ('specific_services', 'Specific Services'),
                        ('category', 'Category'),
                        ('venue', 'Venue'),
                        ('shipping', 'Shipping'),
                    ],
                    default='order_total',
                    help_text='What this discount applies to',
                    max_length=20,
                )),
                ('discount_value', models.DecimalField(
                    decimal_places=2,
                    help_text='Discount value (percentage or amount)',
                    max_digits=10,
                    validators=[django.core.validators.MinValueValidator(Decimal('0.01'))],
                )),
                ('max_discount_amount_currency', djmoney.models.fields.CurrencyField(
                    choices=[('USD', 'USD')],
                    default='USD',
                    editable=False,
                    max_length=3,
                )),
                ('max_discount_amount', djmoney.models.fields.MoneyField(
                    blank=True,
                    decimal_places=2,
                    help_text='Maximum discount amount (for percentage coupons)',
                    max_digits=10,
                    null=True,
                )),
                ('usage_limit', models.PositiveIntegerField(
                    blank=True,
                    help_text='Maximum number of times this coupon can be used',
                    null=True,
                )),
                ('usage_limit_per_user', models.PositiveIntegerField(
                    default=1,
                    help_text='Maximum uses per user',
                )),
                ('current_usage_count', models.PositiveIntegerField(
                    default=0,
                    help_text='Current number of times this coupon has been used',
                )),
                ('valid_from', models.DateTimeField(help_text='When this coupon becomes valid')),
                ('valid_until', models.DateTimeField(help_text='When this coupon expires')),
                ('business_rules', models.JSONField(default=dict, help_text='Business rules for coupon application')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_stackable', models.BooleanField(
                    default=False,
                    help_text='Can this coupon be combined with other discounts?',
                )),
                ('priority', models.PositiveIntegerField(
                    default=10,
                    help_text='Priority for coupon application (higher number = higher priority)',
                )),
                ('created_by', models.ForeignKey(
                    help_text='User who created this coupon',
                    null=True,
                    on_delete=django.db.models.deletion.SET_NULL,
                    related_name='created_coupons',
                    to=settings.AUTH_USER_MODEL,
                )),
            ],
            options={
                'verbose_name': 'Coupon Code',
                'verbose_name_plural': 'Coupon Codes',
                'ordering': ['-priority', '-created_at'],
            },
        ),
        
        # Create CouponUsage model
        migrations.CreateModel(
            name='CouponUsage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('order_reference', models.CharField(
                    help_text='Reference to the order where this coupon was used',
                    max_length=100,
                )),
                ('original_amount_currency', djmoney.models.fields.CurrencyField(
                    choices=[('USD', 'USD')],
                    default='USD',
                    editable=False,
                    max_length=3,
                )),
                ('original_amount', djmoney.models.fields.MoneyField(
                    decimal_places=2,
                    help_text='Original order amount before discount',
                    max_digits=10,
                )),
                ('discount_amount_currency', djmoney.models.fields.CurrencyField(
                    choices=[('USD', 'USD')],
                    default='USD',
                    editable=False,
                    max_length=3,
                )),
                ('discount_amount', djmoney.models.fields.MoneyField(
                    decimal_places=2,
                    help_text='Amount of discount applied',
                    max_digits=10,
                )),
                ('final_amount_currency', djmoney.models.fields.CurrencyField(
                    choices=[('USD', 'USD')],
                    default='USD',
                    editable=False,
                    max_length=3,
                )),
                ('final_amount', djmoney.models.fields.MoneyField(
                    decimal_places=2,
                    help_text='Final order amount after discount',
                    max_digits=10,
                )),
                ('used_at', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('coupon', models.ForeignKey(
                    on_delete=django.db.models.deletion.CASCADE,
                    related_name='usage_instances',
                    to='discount_app.couponcode',
                )),
                ('user', models.ForeignKey(
                    on_delete=django.db.models.deletion.CASCADE,
                    related_name='coupon_usage_history',
                    to=settings.AUTH_USER_MODEL,
                )),
            ],
            options={
                'verbose_name': 'Coupon Usage',
                'verbose_name_plural': 'Coupon Usage Records',
                'ordering': ['-used_at'],
            },
        ),
        
        # Create PromotionCampaign model
        migrations.CreateModel(
            name='PromotionCampaign',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='Campaign name', max_length=255)),
                ('slug', models.SlugField(help_text='URL-friendly campaign identifier', max_length=255, unique=True)),
                ('description', models.TextField(help_text='Campaign description')),
                ('status', models.CharField(
                    choices=[
                        ('draft', 'Draft'),
                        ('active', 'Active'),
                        ('paused', 'Paused'),
                        ('expired', 'Expired'),
                        ('depleted', 'Depleted'),
                    ],
                    default='draft',
                    max_length=20,
                )),
                ('start_date', models.DateTimeField(help_text='Campaign start date')),
                ('end_date', models.DateTimeField(help_text='Campaign end date')),
                ('conditions', models.JSONField(default=list, help_text='Campaign conditions')),
                ('target_users', models.PositiveIntegerField(
                    blank=True,
                    help_text='Target number of users for this campaign',
                    null=True,
                )),
                ('budget_limit_currency', djmoney.models.fields.CurrencyField(
                    choices=[('USD', 'USD')],
                    default='USD',
                    editable=False,
                    max_length=3,
                )),
                ('budget_limit', djmoney.models.fields.MoneyField(
                    blank=True,
                    decimal_places=2,
                    help_text='Maximum budget for this campaign',
                    max_digits=12,
                    null=True,
                )),
                ('current_spend_currency', djmoney.models.fields.CurrencyField(
                    choices=[('USD', 'USD')],
                    default='USD',
                    editable=False,
                    max_length=3,
                )),
                ('current_spend', djmoney.models.fields.MoneyField(
                    decimal_places=2,
                    default_currency='USD',
                    default=0,
                    help_text='Current amount spent on this campaign',
                    max_digits=12,
                )),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(
                    null=True,
                    on_delete=django.db.models.deletion.SET_NULL,
                    related_name='created_campaigns',
                    to=settings.AUTH_USER_MODEL,
                )),
            ],
            options={
                'verbose_name': 'Promotion Campaign',
                'verbose_name_plural': 'Promotion Campaigns',
                'ordering': ['-created_at'],
            },
        ),
        
        # Add indexes for new models
        migrations.AddIndex(
            model_name='couponcode',
            index=models.Index(fields=['code'], name='discount_ap_code_idx'),
        ),
        migrations.AddIndex(
            model_name='couponcode',
            index=models.Index(fields=['status', 'valid_from', 'valid_until'], name='discount_ap_status_validity_idx'),
        ),
        migrations.AddIndex(
            model_name='couponcode',
            index=models.Index(fields=['-created_at'], name='discount_ap_created_at_idx'),
        ),
        migrations.AddIndex(
            model_name='couponusage',
            index=models.Index(fields=['coupon', '-used_at'], name='discount_ap_coupon_usage_idx'),
        ),
        migrations.AddIndex(
            model_name='couponusage',
            index=models.Index(fields=['user', '-used_at'], name='discount_ap_user_coupon_usage_idx'),
        ),
        
        # Create many-to-many relationship for coupon allowed users
        migrations.CreateModel(
            name='CouponCode_allowed_users',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('couponcode', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='discount_app.couponcode')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'discount_app_couponcode_allowed_users',
            },
        ),
        
        # Create many-to-many relationship for campaign coupons
        migrations.CreateModel(
            name='PromotionCampaign_coupons',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('promotioncampaign', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='discount_app.promotioncampaign')),
                ('couponcode', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='discount_app.couponcode')),
            ],
            options={
                'db_table': 'discount_app_promotioncampaign_coupons',
            },
        ),
        
        # Add unique constraints for many-to-many relationships
        migrations.AlterUniqueTogether(
            name='CouponCode_allowed_users',
            unique_together={('couponcode', 'user')},
        ),
        migrations.AlterUniqueTogether(
            name='PromotionCampaign_coupons',
            unique_together={('promotioncampaign', 'couponcode')},
        ),
    ] 