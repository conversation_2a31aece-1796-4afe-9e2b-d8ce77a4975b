"""
Business Rules Engine for CozyWish Discount System.
This module provides comprehensive business logic for discount validation,
eligibility checking, coupon combination, and discount calculation.
"""

import logging
from decimal import Decimal
from datetime import datetime, time
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db.models import Sum, Q

from djmoney.money import Money
from pydantic import ValidationError as PydanticValidationError

from .schemas import (
    ComprehensiveDiscountRulesSchema,
    CouponValidationSchema,
    DiscountCalculationSchema,
    UserEligibilitySchema,
    BookingEligibilitySchema,
    TimeRestrictionSchema,
    UsageLimitSchema,
    CombinationRulesSchema,
    DiscountTypeEnum,
    CouponTypeEnum,
    UserTypeEnum,
)

logger = logging.getLogger(__name__)
User = get_user_model()


@dataclass
class DiscountContext:
    """Context object containing all information needed for discount validation."""
    user: Optional[User] = None
    booking_total: Optional[Decimal] = None
    category_ids: Optional[List[int]] = None
    venue_ids: Optional[List[int]] = None
    service_ids: Optional[List[int]] = None
    item_count: Optional[int] = None
    booking_date: Optional[datetime] = None
    existing_discounts: Optional[List[Any]] = None
    existing_coupons: Optional[List[Any]] = None


@dataclass
class DiscountResult:
    """Result object containing discount calculation and validation results."""
    is_eligible: bool
    discount_amount: Decimal
    final_amount: Decimal
    applied_rules: List[str]
    error_messages: List[str]
    warnings: List[str]


class BusinessRulesEngine:
    """
    Comprehensive business rules engine for discount validation and application.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__ + '.BusinessRulesEngine')
    
    def validate_discount_rules(self, rules: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate discount rules against the comprehensive schema.
        
        Args:
            rules: Dictionary containing discount rules
            
        Returns:
            Tuple of (is_valid, error_messages)
        """
        try:
            ComprehensiveDiscountRulesSchema(**rules)
            return True, []
        except PydanticValidationError as e:
            error_messages = [str(error) for error in e.errors()]
            return False, error_messages
    
    def check_user_eligibility(self, user: User, rules: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Check if a user is eligible for a discount based on user eligibility rules.
        
        Args:
            user: User object
            rules: User eligibility rules
            
        Returns:
            Tuple of (is_eligible, reasons)
        """
        if not rules:
            return True, []
            
        try:
            user_rules = UserEligibilitySchema(**rules)
        except PydanticValidationError as e:
            return False, [f"Invalid user eligibility rules: {str(e)}"]
        
        reasons = []
        
        # Check if new users only
        if user_rules.new_users_only:
            if not self._is_new_user(user):
                reasons.append("Discount is only available for new users")
        
        # Check account age restrictions
        if user_rules.min_account_age_days or user_rules.max_account_age_days:
            account_age_days = (timezone.now() - user.date_joined).days
            
            if user_rules.min_account_age_days and account_age_days < user_rules.min_account_age_days:
                reasons.append(f"Account must be at least {user_rules.min_account_age_days} days old")
            
            if user_rules.max_account_age_days and account_age_days > user_rules.max_account_age_days:
                reasons.append(f"Account must be less than {user_rules.max_account_age_days} days old")
        
        # Check previous bookings restrictions
        if user_rules.min_previous_bookings or user_rules.max_previous_bookings:
            booking_count = self._get_user_booking_count(user)
            
            if user_rules.min_previous_bookings and booking_count < user_rules.min_previous_bookings:
                reasons.append(f"User must have at least {user_rules.min_previous_bookings} previous bookings")
            
            if user_rules.max_previous_bookings and booking_count > user_rules.max_previous_bookings:
                reasons.append(f"User must have no more than {user_rules.max_previous_bookings} previous bookings")
        
        # Check user type restrictions
        if user_rules.user_types:
            user_type = self._get_user_type(user)
            if user_type not in user_rules.user_types:
                reasons.append(f"User type '{user_type}' is not eligible for this discount")
        
        # Check excluded users
        if user_rules.excluded_user_ids and user.id in user_rules.excluded_user_ids:
            reasons.append("User is excluded from this discount")
        
        return len(reasons) == 0, reasons
    
    def check_booking_eligibility(self, context: DiscountContext, rules: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Check if a booking is eligible for a discount based on booking eligibility rules.
        
        Args:
            context: Discount context containing booking information
            rules: Booking eligibility rules
            
        Returns:
            Tuple of (is_eligible, reasons)
        """
        if not rules:
            return True, []
            
        try:
            booking_rules = BookingEligibilitySchema(**rules)
        except PydanticValidationError as e:
            return False, [f"Invalid booking eligibility rules: {str(e)}"]
        
        reasons = []
        
        # Check booking value restrictions
        if context.booking_total is not None:
            if booking_rules.min_booking_value and context.booking_total < booking_rules.min_booking_value:
                reasons.append(f"Booking total must be at least ${booking_rules.min_booking_value}")
            
            if booking_rules.max_booking_value and context.booking_total > booking_rules.max_booking_value:
                reasons.append(f"Booking total must be no more than ${booking_rules.max_booking_value}")
        
        # Check item count restrictions
        if context.item_count is not None:
            if booking_rules.min_items and context.item_count < booking_rules.min_items:
                reasons.append(f"Booking must have at least {booking_rules.min_items} items")
            
            if booking_rules.max_items and context.item_count > booking_rules.max_items:
                reasons.append(f"Booking must have no more than {booking_rules.max_items} items")
        
        # Check category restrictions
        if context.category_ids:
            if booking_rules.allowed_categories:
                if not any(cat_id in booking_rules.allowed_categories for cat_id in context.category_ids):
                    reasons.append("Booking must include items from allowed categories")
            
            if booking_rules.excluded_categories:
                if any(cat_id in booking_rules.excluded_categories for cat_id in context.category_ids):
                    reasons.append("Booking includes items from excluded categories")
        
        # Check venue restrictions
        if context.venue_ids:
            if booking_rules.allowed_venues:
                if not any(venue_id in booking_rules.allowed_venues for venue_id in context.venue_ids):
                    reasons.append("Booking must include items from allowed venues")
            
            if booking_rules.excluded_venues:
                if any(venue_id in booking_rules.excluded_venues for venue_id in context.venue_ids):
                    reasons.append("Booking includes items from excluded venues")
        
        # Check service restrictions
        if context.service_ids:
            if booking_rules.allowed_services:
                if not any(service_id in booking_rules.allowed_services for service_id in context.service_ids):
                    reasons.append("Booking must include allowed services")
            
            if booking_rules.excluded_services:
                if any(service_id in booking_rules.excluded_services for service_id in context.service_ids):
                    reasons.append("Booking includes excluded services")
        
        return len(reasons) == 0, reasons
    
    def check_time_restrictions(self, context: DiscountContext, rules: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Check if current time/date meets time restriction requirements.
        
        Args:
            context: Discount context
            rules: Time restriction rules
            
        Returns:
            Tuple of (is_eligible, reasons)
        """
        if not rules:
            return True, []
            
        try:
            time_rules = TimeRestrictionSchema(**rules)
        except PydanticValidationError as e:
            return False, [f"Invalid time restriction rules: {str(e)}"]
        
        reasons = []
        now = timezone.now()
        booking_date = context.booking_date or now
        
        # Check time of day restrictions
        if time_rules.start_time and time_rules.end_time:
            current_time = booking_date.time()
            if not (time_rules.start_time <= current_time <= time_rules.end_time):
                reasons.append(f"Discount is only available between {time_rules.start_time} and {time_rules.end_time}")
        
        # Check days of week restrictions
        if time_rules.days_of_week:
            booking_weekday = booking_date.weekday()  # 0=Monday, 6=Sunday
            if booking_weekday not in time_rules.days_of_week:
                reasons.append("Discount is not available on this day of the week")
        
        # Check specific dates
        if time_rules.specific_dates:
            booking_date_only = booking_date.date()
            allowed_dates = [d.date() for d in time_rules.specific_dates]
            if booking_date_only not in allowed_dates:
                reasons.append("Discount is only available on specific dates")
        
        # Check excluded dates
        if time_rules.exclude_dates:
            booking_date_only = booking_date.date()
            excluded_dates = [d.date() for d in time_rules.exclude_dates]
            if booking_date_only in excluded_dates:
                reasons.append("Discount is not available on this date")
        
        return len(reasons) == 0, reasons
    
    def check_usage_limits(self, user: User, discount_obj: Any, rules: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Check if usage limits have been exceeded.
        
        Args:
            user: User object
            discount_obj: Discount object
            rules: Usage limit rules
            
        Returns:
            Tuple of (is_eligible, reasons)
        """
        if not rules:
            return True, []
            
        try:
            usage_rules = UsageLimitSchema(**rules)
        except PydanticValidationError as e:
            return False, [f"Invalid usage limit rules: {str(e)}"]
        
        reasons = []
        
        # Check max uses per user
        if usage_rules.max_uses_per_user:
            user_usage_count = self._get_user_discount_usage_count(user, discount_obj)
            if user_usage_count >= usage_rules.max_uses_per_user:
                reasons.append(f"User has exceeded maximum uses per user ({usage_rules.max_uses_per_user})")
        
        # Check max total uses
        if usage_rules.max_total_uses:
            total_usage_count = self._get_total_discount_usage_count(discount_obj)
            if total_usage_count >= usage_rules.max_total_uses:
                reasons.append(f"Discount has exceeded maximum total uses ({usage_rules.max_total_uses})")
        
        # Check cooldown period
        if usage_rules.cooldown_period_hours:
            last_use = self._get_last_user_discount_usage(user, discount_obj)
            if last_use:
                hours_since_last_use = (timezone.now() - last_use).total_seconds() / 3600
                if hours_since_last_use < usage_rules.cooldown_period_hours:
                    reasons.append(f"Cooldown period of {usage_rules.cooldown_period_hours} hours has not passed")
        
        return len(reasons) == 0, reasons
    
    def check_combination_rules(self, context: DiscountContext, rules: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Check if discount can be combined with existing discounts/coupons.
        
        Args:
            context: Discount context
            rules: Combination rules
            
        Returns:
            Tuple of (is_eligible, reasons)
        """
        if not rules:
            return True, []
            
        try:
            combo_rules = CombinationRulesSchema(**rules)
        except PydanticValidationError as e:
            return False, [f"Invalid combination rules: {str(e)}"]
        
        reasons = []
        
        # Check if can combine with existing coupons
        if not combo_rules.can_combine_with_coupons and context.existing_coupons:
            reasons.append("Discount cannot be combined with coupon codes")
        
        # Check if can combine with existing discounts
        if not combo_rules.can_combine_with_discounts and context.existing_discounts:
            reasons.append("Discount cannot be combined with other discounts")
        
        # Check excluded coupon types
        if combo_rules.excluded_coupon_types and context.existing_coupons:
            for coupon in context.existing_coupons:
                if coupon.coupon_type in combo_rules.excluded_coupon_types:
                    reasons.append(f"Discount cannot be combined with {coupon.coupon_type} coupons")
        
        # Check maximum total discount percentage
        if combo_rules.max_total_discount_percentage and context.booking_total:
            total_existing_discount = self._calculate_existing_discount_amount(context)
            existing_discount_percentage = (total_existing_discount / context.booking_total) * 100
            
            if existing_discount_percentage >= combo_rules.max_total_discount_percentage:
                reasons.append(f"Total discount percentage would exceed maximum ({combo_rules.max_total_discount_percentage}%)")
        
        return len(reasons) == 0, reasons
    
    def calculate_discount(self, discount_obj: Any, context: DiscountContext) -> DiscountResult:
        """
        Calculate the final discount amount based on all business rules.
        
        Args:
            discount_obj: Discount object
            context: Discount context
            
        Returns:
            DiscountResult object with calculation results
        """
        applied_rules = []
        error_messages = []
        warnings = []
        
        # Validate discount rules
        if hasattr(discount_obj, 'business_rules') and discount_obj.business_rules:
            is_valid, validation_errors = self.validate_discount_rules(discount_obj.business_rules)
            if not is_valid:
                return DiscountResult(
                    is_eligible=False,
                    discount_amount=Decimal('0'),
                    final_amount=context.booking_total or Decimal('0'),
                    applied_rules=applied_rules,
                    error_messages=validation_errors,
                    warnings=warnings
                )
        
        # Check user eligibility
        if context.user and hasattr(discount_obj, 'business_rules') and discount_obj.business_rules:
            user_rules = discount_obj.business_rules.get('user_eligibility', {})
            is_eligible, reasons = self.check_user_eligibility(context.user, user_rules)
            if not is_eligible:
                error_messages.extend(reasons)
        
        # Check booking eligibility
        if hasattr(discount_obj, 'business_rules') and discount_obj.business_rules:
            booking_rules = discount_obj.business_rules.get('booking_eligibility', {})
            is_eligible, reasons = self.check_booking_eligibility(context, booking_rules)
            if not is_eligible:
                error_messages.extend(reasons)
        
        # Check time restrictions
        if hasattr(discount_obj, 'business_rules') and discount_obj.business_rules:
            time_rules = discount_obj.business_rules.get('time_restrictions', {})
            is_eligible, reasons = self.check_time_restrictions(context, time_rules)
            if not is_eligible:
                error_messages.extend(reasons)
        
        # Check usage limits
        if context.user and hasattr(discount_obj, 'business_rules') and discount_obj.business_rules:
            usage_rules = discount_obj.business_rules.get('usage_limits', {})
            is_eligible, reasons = self.check_usage_limits(context.user, discount_obj, usage_rules)
            if not is_eligible:
                error_messages.extend(reasons)
        
        # Check combination rules
        if hasattr(discount_obj, 'business_rules') and discount_obj.business_rules:
            combo_rules = discount_obj.business_rules.get('combination_rules', {})
            is_eligible, reasons = self.check_combination_rules(context, combo_rules)
            if not is_eligible:
                error_messages.extend(reasons)
        
        # If there are any errors, return ineligible result
        if error_messages:
            return DiscountResult(
                is_eligible=False,
                discount_amount=Decimal('0'),
                final_amount=context.booking_total or Decimal('0'),
                applied_rules=applied_rules,
                error_messages=error_messages,
                warnings=warnings
            )
        
        # Calculate discount amount
        if context.booking_total:
            discount_amount = discount_obj.calculate_discount_amount(context.booking_total)
            final_amount = context.booking_total - discount_amount
            
            # Ensure final amount is not negative
            if final_amount < Decimal('0'):
                final_amount = Decimal('0')
                discount_amount = context.booking_total
                warnings.append("Discount amount was capped to prevent negative total")
        else:
            discount_amount = Decimal('0')
            final_amount = Decimal('0')
        
        applied_rules.append(f"Applied {discount_obj.__class__.__name__} discount")
        
        return DiscountResult(
            is_eligible=True,
            discount_amount=discount_amount,
            final_amount=final_amount,
            applied_rules=applied_rules,
            error_messages=error_messages,
            warnings=warnings
        )
    
    def validate_coupon_code(self, coupon_code: str, context: DiscountContext) -> Tuple[bool, List[str], Any]:
        """
        Validate a coupon code against business rules.
        
        Args:
            coupon_code: Coupon code to validate
            context: Discount context
            
        Returns:
            Tuple of (is_valid, error_messages, coupon_object)
        """
        # This method would integrate with the CouponCode model
        # For now, returning a placeholder implementation
        try:
            from .models.coupon_models import CouponCode
            
            try:
                coupon = CouponCode.objects.get(code=coupon_code.upper())
            except CouponCode.DoesNotExist:
                return False, ["Invalid coupon code"], None
            
            # Check if coupon is active
            if not coupon.is_valid(context.user, context.booking_total):
                return False, ["Coupon code is not valid"], None
            
            return True, [], coupon
            
        except ImportError:
            return False, ["Coupon system not available"], None
    
    def _is_new_user(self, user: User) -> bool:
        """Check if user is considered a new user."""
        # Consider users registered within the last 30 days as new
        threshold = timezone.now() - timezone.timedelta(days=30)
        return user.date_joined >= threshold
    
    def _get_user_booking_count(self, user: User) -> int:
        """Get the number of bookings for a user."""
        # This would integrate with the booking system
        # For now, returning 0 as placeholder
        return 0
    
    def _get_user_type(self, user: User) -> str:
        """Get the user type for eligibility checking."""
        # This would determine user type based on business logic
        # For now, returning default type
        return UserTypeEnum.RETURNING_USER
    
    def _get_user_discount_usage_count(self, user: User, discount_obj: Any) -> int:
        """Get the number of times a user has used a specific discount."""
        from .models import DiscountUsage
        
        return DiscountUsage.objects.filter(
            user=user,
            discount_type=discount_obj.__class__.__name__,
            discount_id=discount_obj.id
        ).count()
    
    def _get_total_discount_usage_count(self, discount_obj: Any) -> int:
        """Get the total number of times a discount has been used."""
        return getattr(discount_obj, 'current_usage_count', 0)
    
    def _get_last_user_discount_usage(self, user: User, discount_obj: Any) -> Optional[datetime]:
        """Get the last time a user used a specific discount."""
        from .models import DiscountUsage
        
        usage = DiscountUsage.objects.filter(
            user=user,
            discount_type=discount_obj.__class__.__name__,
            discount_id=discount_obj.id
        ).order_by('-used_at').first()
        
        return usage.used_at if usage else None
    
    def _calculate_existing_discount_amount(self, context: DiscountContext) -> Decimal:
        """Calculate the total amount of existing discounts/coupons."""
        total_discount = Decimal('0')
        
        if context.existing_discounts:
            for discount in context.existing_discounts:
                total_discount += discount.get('discount_amount', Decimal('0'))
        
        if context.existing_coupons:
            for coupon in context.existing_coupons:
                total_discount += coupon.get('discount_amount', Decimal('0'))
        
        return total_discount


# Global instance for easy access
business_rules_engine = BusinessRulesEngine()


def validate_discount_eligibility(discount_obj: Any, user: User, booking_total: Decimal, 
                                 category_ids: List[int] = None, venue_ids: List[int] = None,
                                 service_ids: List[int] = None, item_count: int = None) -> DiscountResult:
    """
    Convenience function to validate discount eligibility.
    
    Args:
        discount_obj: Discount object to validate
        user: User object
        booking_total: Total booking amount
        category_ids: List of category IDs in the booking
        venue_ids: List of venue IDs in the booking
        service_ids: List of service IDs in the booking
        item_count: Number of items in the booking
        
    Returns:
        DiscountResult object with validation results
    """
    context = DiscountContext(
        user=user,
        booking_total=booking_total,
        category_ids=category_ids or [],
        venue_ids=venue_ids or [],
        service_ids=service_ids or [],
        item_count=item_count,
        booking_date=timezone.now()
    )
    
    return business_rules_engine.calculate_discount(discount_obj, context)


def validate_coupon_eligibility(coupon_code: str, user: User, booking_total: Decimal,
                               category_ids: List[int] = None, venue_ids: List[int] = None,
                               service_ids: List[int] = None) -> Tuple[bool, List[str], Any]:
    """
    Convenience function to validate coupon eligibility.
    
    Args:
        coupon_code: Coupon code to validate
        user: User object
        booking_total: Total booking amount
        category_ids: List of category IDs in the booking
        venue_ids: List of venue IDs in the booking
        service_ids: List of service IDs in the booking
        
    Returns:
        Tuple of (is_valid, error_messages, coupon_object)
    """
    context = DiscountContext(
        user=user,
        booking_total=booking_total,
        category_ids=category_ids or [],
        venue_ids=venue_ids or [],
        service_ids=service_ids or [],
        booking_date=timezone.now()
    )
    
    return business_rules_engine.validate_coupon_code(coupon_code, context) 