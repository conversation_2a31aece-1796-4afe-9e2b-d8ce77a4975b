"""Enhanced admin configuration for discount management."""

# --- Third-Party Imports ---
from django.contrib import admin
from django.utils import timezone
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Sum, Count, Q
from django.forms import ModelForm
from django.core.exceptions import ValidationError

# --- Local App Imports ---
from .models import (
    DiscountUsage, PlatformDiscount, ServiceDiscount, VenueDiscount,
    CouponCode, CouponUsage, PromotionCampaign
)


class EnhancedDiscountAdminMixin:
    """Mixin with common functionality for discount admin interfaces."""
    
    def discount_display(self, obj):
        """Display discount value with type."""
        if obj.discount_type == "percentage":
            return f"{obj.discount_value}%"
        return f"${obj.discount_value}"
    discount_display.short_description = "Discount"

    def status_display(self, obj):
        """Display current status with color coding."""
        status = obj.get_status()
        colors = {
            "active": "green",
            "scheduled": "orange", 
            "expired": "red",
            "cancelled": "gray",
        }
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(status, "black"),
            status.title(),
        )
    status_display.short_description = "Status"
    
    def usage_count_display(self, obj):
        """Display current usage count with limit."""
        if obj.max_uses:
            return f"{obj.current_usage_count}/{obj.max_uses}"
        return f"{obj.current_usage_count}/∞"
    usage_count_display.short_description = "Usage"
    
    def priority_display(self, obj):
        """Display priority with visual indicator."""
        if obj.priority >= 20:
            color = "red"
        elif obj.priority >= 10:
            color = "orange"
        else:
            color = "green"
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.priority,
        )
    priority_display.short_description = "Priority"


@admin.register(VenueDiscount)
class VenueDiscountAdmin(EnhancedDiscountAdminMixin, admin.ModelAdmin):
    """Enhanced admin interface for venue discounts."""

    list_display = [
        "name",
        "venue",
        "discount_display",
        "status_display",
        "is_approved",
        "usage_count_display",
        "priority_display",
        "is_stackable",
        "start_date",
        "end_date",
        "created_at",
    ]
    list_filter = [
        "discount_type",
        "is_approved",
        "is_stackable",
        "priority",
        "start_date",
        "end_date",
        "created_at",
    ]
    search_fields = ["name", "venue__venue_name", "description"]
    readonly_fields = ["created_at", "updated_at", "approved_at", "current_usage_count"]

    fieldsets = (
        ("Basic Information", {"fields": ("name", "description", "venue")}),
        (
            "Discount Configuration",
            {
                "fields": (
                    "discount_type",
                    "discount_value",
                    "min_booking_value",
                    "max_discount_amount",
                    "max_uses",
                )
            },
        ),
        ("Schedule", {"fields": ("start_date", "end_date")}),
        ("Advanced Settings", {
            "fields": ("priority", "is_stackable", "business_rules"),
            "classes": ("collapse",),
        }),
        ("Approval", {"fields": ("is_approved", "approved_by", "approved_at")}),
        ("Usage Tracking", {
            "fields": ("current_usage_count",),
            "classes": ("collapse",),
        }),
        (
            "Metadata",
            {
                "fields": ("created_by", "created_at", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )

    actions = ["approve_discounts", "deactivate_discounts"]

    def approve_discounts(self, request, queryset):
        """Bulk approve selected discounts."""
        updated = queryset.update(
            is_approved=True,
            approved_by=request.user,
            approved_at=timezone.now()
        )
        self.message_user(request, f"{updated} discounts approved successfully.")
    approve_discounts.short_description = "Approve selected discounts"

    def deactivate_discounts(self, request, queryset):
        """Bulk deactivate selected discounts."""
        updated = queryset.update(end_date=timezone.now())
        self.message_user(request, f"{updated} discounts deactivated successfully.")
    deactivate_discounts.short_description = "Deactivate selected discounts"


@admin.register(ServiceDiscount)
class ServiceDiscountAdmin(EnhancedDiscountAdminMixin, admin.ModelAdmin):
    """Enhanced admin interface for service discounts."""

    list_display = [
        "name",
        "service",
        "discount_display",
        "status_display",
        "is_approved",
        "usage_count_display",
        "priority_display",
        "is_stackable",
        "start_date",
        "end_date",
        "created_at",
    ]
    list_filter = [
        "discount_type",
        "is_approved",
        "is_stackable",
        "priority",
        "start_date",
        "end_date",
        "created_at",
    ]
    search_fields = [
        "name",
        "service__service_title",
        "service__venue__venue_name",
        "description",
    ]
    readonly_fields = ["created_at", "updated_at", "approved_at", "current_usage_count"]

    fieldsets = (
        ("Basic Information", {"fields": ("name", "description", "service")}),
        ("Discount Configuration", {"fields": ("discount_type", "discount_value", "max_uses")}),
        ("Schedule", {"fields": ("start_date", "end_date")}),
        ("Advanced Settings", {
            "fields": ("priority", "is_stackable", "business_rules"),
            "classes": ("collapse",),
        }),
        ("Approval", {"fields": ("is_approved", "approved_by", "approved_at")}),
        ("Usage Tracking", {
            "fields": ("current_usage_count",),
            "classes": ("collapse",),
        }),
        (
            "Metadata",
            {
                "fields": ("created_by", "created_at", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )

    actions = ["approve_discounts", "deactivate_discounts"]

    def approve_discounts(self, request, queryset):
        """Bulk approve selected discounts."""
        updated = queryset.update(
            is_approved=True,
            approved_by=request.user,
            approved_at=timezone.now()
        )
        self.message_user(request, f"{updated} discounts approved successfully.")
    approve_discounts.short_description = "Approve selected discounts"

    def deactivate_discounts(self, request, queryset):
        """Bulk deactivate selected discounts."""
        updated = queryset.update(end_date=timezone.now())
        self.message_user(request, f"{updated} discounts deactivated successfully.")
    deactivate_discounts.short_description = "Deactivate selected discounts"


@admin.register(PlatformDiscount)
class PlatformDiscountAdmin(EnhancedDiscountAdminMixin, admin.ModelAdmin):
    """Enhanced admin interface for platform discounts."""

    list_display = [
        "name",
        "category",
        "discount_display",
        "status_display",
        "is_featured",
        "usage_count_display",
        "priority_display",
        "is_stackable",
        "start_date",
        "end_date",
        "created_at",
    ]
    list_filter = [
        "discount_type",
        "is_featured",
        "is_stackable",
        "priority",
        "category",
        "start_date",
        "end_date",
        "created_at",
    ]
    search_fields = ["name", "description", "category__name"]
    readonly_fields = ["created_at", "updated_at", "current_usage_count"]

    fieldsets = (
        (
            "Basic Information",
            {"fields": ("name", "description", "category", "is_featured")},
        ),
        (
            "Discount Configuration",
            {
                "fields": (
                    "discount_type",
                    "discount_value",
                    "min_booking_value",
                    "max_discount_amount",
                    "max_uses",
                )
            },
        ),
        ("Schedule", {"fields": ("start_date", "end_date")}),
        ("Advanced Settings", {
            "fields": ("priority", "is_stackable", "business_rules"),
            "classes": ("collapse",),
        }),
        ("Usage Tracking", {
            "fields": ("current_usage_count",),
            "classes": ("collapse",),
        }),
        (
            "Metadata",
            {
                "fields": ("created_by", "created_at", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )

    actions = ["feature_discounts", "deactivate_discounts"]

    def feature_discounts(self, request, queryset):
        """Bulk feature selected discounts."""
        updated = queryset.update(is_featured=True)
        self.message_user(request, f"{updated} discounts featured successfully.")
    feature_discounts.short_description = "Feature selected discounts"

    def deactivate_discounts(self, request, queryset):
        """Bulk deactivate selected discounts."""
        updated = queryset.update(end_date=timezone.now())
        self.message_user(request, f"{updated} discounts deactivated successfully.")
    deactivate_discounts.short_description = "Deactivate selected discounts"


@admin.register(CouponCode)
class CouponCodeAdmin(admin.ModelAdmin):
    """Enhanced admin interface for coupon codes."""

    list_display = [
        "code",
        "name",
        "coupon_type",
        "status",
        "discount_display",
        "usage_count_display",
        "priority_display",
        "is_stackable",
        "valid_from",
        "valid_until",
        "created_at",
    ]
    list_filter = [
        "coupon_type",
        "status",
        "target",
        "is_stackable",
        "priority",
        "valid_from",
        "valid_until",
        "created_at",
    ]
    search_fields = ["code", "name", "description"]
    readonly_fields = ["created_at", "updated_at", "current_usage_count"]

    fieldsets = (
        ("Basic Information", {"fields": ("name", "description", "code")}),
        ("Coupon Configuration", {
            "fields": (
                "coupon_type",
                "status",
                "target",
                "discount_value",
                "max_discount_amount",
            )
        }),
        ("Usage Limits", {
            "fields": (
                "usage_limit",
                "usage_limit_per_user",
                "current_usage_count",
            )
        }),
        ("Schedule", {"fields": ("valid_from", "valid_until")}),
        ("Advanced Settings", {
            "fields": ("priority", "is_stackable", "business_rules"),
            "classes": ("collapse",),
        }),
        ("Access Control", {
            "fields": ("allowed_users",),
            "classes": ("collapse",),
        }),
        (
            "Metadata",
            {
                "fields": ("created_by", "created_at", "updated_at"),
                "classes": ("collapse",),
            },
        ),
    )

    filter_horizontal = ["allowed_users"]
    actions = ["activate_coupons", "deactivate_coupons", "generate_coupon_codes"]

    def discount_display(self, obj):
        """Display discount value with type."""
        if obj.coupon_type == "percentage":
            return f"{obj.discount_value}%"
        elif obj.coupon_type == "fixed_amount":
            return f"${obj.discount_value}"
        else:
            return obj.coupon_type.replace("_", " ").title()
    discount_display.short_description = "Discount"

    def usage_count_display(self, obj):
        """Display current usage count with limit."""
        if obj.usage_limit:
            return f"{obj.current_usage_count}/{obj.usage_limit}"
        return f"{obj.current_usage_count}/∞"
    usage_count_display.short_description = "Usage"

    def priority_display(self, obj):
        """Display priority with visual indicator."""
        if obj.priority >= 20:
            color = "red"
        elif obj.priority >= 10:
            color = "orange"
        else:
            color = "green"
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.priority,
        )
    priority_display.short_description = "Priority"

    def activate_coupons(self, request, queryset):
        """Bulk activate selected coupons."""
        updated = queryset.update(status="active")
        self.message_user(request, f"{updated} coupons activated successfully.")
    activate_coupons.short_description = "Activate selected coupons"

    def deactivate_coupons(self, request, queryset):
        """Bulk deactivate selected coupons."""
        updated = queryset.update(status="paused")
        self.message_user(request, f"{updated} coupons deactivated successfully.")
    deactivate_coupons.short_description = "Deactivate selected coupons"

    def generate_coupon_codes(self, request, queryset):
        """Generate new coupon codes for selected coupons."""
        for coupon in queryset:
            if not coupon.code:
                coupon.code = CouponCode.generate_unique_code()
                coupon.save()
        self.message_user(request, f"Generated codes for {queryset.count()} coupons.")
    generate_coupon_codes.short_description = "Generate coupon codes"


@admin.register(CouponUsage)
class CouponUsageAdmin(admin.ModelAdmin):
    """Admin interface for coupon usage tracking."""

    list_display = [
        "coupon",
        "user",
        "order_reference",
        "original_amount",
        "discount_amount",
        "final_amount",
        "savings_percentage",
        "used_at",
    ]
    list_filter = [
        "coupon__coupon_type",
        "used_at",
        "coupon__status",
    ]
    search_fields = [
        "coupon__code",
        "coupon__name",
        "user__email",
        "order_reference",
    ]
    readonly_fields = ["used_at", "savings_percentage", "savings_amount"]

    fieldsets = (
        ("Coupon Usage", {"fields": ("coupon", "user", "order_reference")}),
        ("Financial Details", {
            "fields": (
                "original_amount",
                "discount_amount",
                "final_amount",
                "savings_percentage",
                "savings_amount",
            )
        }),
        ("Metadata", {
            "fields": ("used_at", "ip_address", "user_agent"),
            "classes": ("collapse",),
        }),
    )

    def savings_percentage(self, obj):
        """Calculate savings percentage."""
        if obj.original_amount and obj.original_amount.amount > 0:
            return f"{((obj.discount_amount.amount / obj.original_amount.amount) * 100):.1f}%"
        return "0%"
    savings_percentage.short_description = "Savings %"

    def savings_amount(self, obj):
        """Display savings amount."""
        return obj.discount_amount
    savings_amount.short_description = "Savings"


@admin.register(PromotionCampaign)
class PromotionCampaignAdmin(admin.ModelAdmin):
    """Admin interface for promotion campaigns."""

    list_display = [
        "name",
        "status",
        "coupon_count",
        "target_users",
        "budget_display",
        "start_date",
        "end_date",
        "created_at",
    ]
    list_filter = [
        "status",
        "start_date",
        "end_date",
        "created_at",
    ]
    search_fields = ["name", "description", "slug"]
    readonly_fields = ["created_at", "updated_at", "current_spend"]

    fieldsets = (
        ("Basic Information", {"fields": ("name", "slug", "description")}),
        ("Campaign Settings", {
            "fields": ("status", "start_date", "end_date", "target_users")
        }),
        ("Budget Management", {
            "fields": ("budget_limit", "current_spend")
        }),
        ("Advanced Settings", {
            "fields": ("conditions",),
            "classes": ("collapse",),
        }),
        ("Associated Coupons", {
            "fields": ("coupons",),
            "classes": ("collapse",),
        }),
        ("Metadata", {
            "fields": ("created_by", "created_at", "updated_at"),
            "classes": ("collapse",),
        }),
    )

    filter_horizontal = ["coupons"]
    actions = ["activate_campaigns", "deactivate_campaigns"]

    def coupon_count(self, obj):
        """Display number of associated coupons."""
        return obj.coupons.count()
    coupon_count.short_description = "Coupons"

    def budget_display(self, obj):
        """Display budget usage."""
        if obj.budget_limit:
            percentage = (obj.current_spend.amount / obj.budget_limit.amount) * 100
            if percentage >= 90:
                color = "red"
            elif percentage >= 70:
                color = "orange"
            else:
                color = "green"
            return format_html(
                '<span style="color: {};">{} / {} ({:.1f}%)</span>',
                color,
                obj.current_spend,
                obj.budget_limit,
                percentage,
            )
        return obj.current_spend
    budget_display.short_description = "Budget"

    def activate_campaigns(self, request, queryset):
        """Bulk activate selected campaigns."""
        updated = queryset.update(status="active")
        self.message_user(request, f"{updated} campaigns activated successfully.")
    activate_campaigns.short_description = "Activate selected campaigns"

    def deactivate_campaigns(self, request, queryset):
        """Bulk deactivate selected campaigns."""
        updated = queryset.update(status="paused")
        self.message_user(request, f"{updated} campaigns deactivated successfully.")
    deactivate_campaigns.short_description = "Deactivate selected campaigns"


@admin.register(DiscountUsage)
class DiscountUsageAdmin(admin.ModelAdmin):
    """Enhanced admin interface for discount usage tracking."""

    list_display = [
        "user",
        "discount_type",
        "discount_name",
        "original_price",
        "discount_amount",
        "final_price",
        "savings_percentage",
        "used_at",
    ]
    list_filter = ["discount_type", "used_at"]
    search_fields = ["user__email", "booking_reference"]
    readonly_fields = ["used_at", "savings_percentage", "savings_amount"]

    fieldsets = (
        ("Usage Details", {"fields": ("user", "discount_type", "discount_id", "booking_reference")}),
        ("Financial Details", {
            "fields": (
                "original_price",
                "discount_amount",
                "final_price",
                "savings_percentage",
                "savings_amount",
            )
        }),
        ("Metadata", {
            "fields": ("used_at", "ip_address"),
            "classes": ("collapse",),
        }),
    )

    def discount_name(self, obj):
        """Get the name of the discount used."""
        discount = obj.get_discount_object()
        return discount.name if discount else "N/A"
    discount_name.short_description = "Discount Name"

    def savings_percentage(self, obj):
        """Calculate savings percentage."""
        return f"{obj.get_savings_percentage()}%"
    savings_percentage.short_description = "Savings %"

    def savings_amount(self, obj):
        """Display savings amount."""
        return f"${obj.get_savings_amount():.2f}"
    savings_amount.short_description = "Savings"


# Admin site customization
admin.site.site_header = "CozyWish Discount Management"
admin.site.site_title = "CozyWish Discounts"
admin.site.index_title = "Discount & Coupon Administration"
