# Standard library imports
import logging
from decimal import Decimal
from typing import Optional, List, Dict, Any

from django.apps import apps
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator

# Django imports
from django.db import models
from django.utils import timezone
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _

# Third-party imports
from djmoney.models.fields import MoneyField
from djmoney.money import Money
from django_lifecycle import LifecycleModel, hook, AFTER_CREATE, BEFORE_SAVE, AFTER_SAVE
from pydantic import BaseModel, Field, validator
from django_pydantic_field import SchemaField

# Local imports
from venues_app.models import Category, Service, Venue

# Logger
logger = logging.getLogger(__name__)

# Additional imports for coupon models
import uuid
import secrets
import string


class DiscountType(models.TextChoices):
    """Discount type choices for different discount calculation methods."""

    PERCENTAGE = "percentage", _("Percentage")
    FIXED_AMOUNT = "fixed_amount", _("Fixed Amount")


class DiscountStatus(models.TextChoices):
    """Discount status choices for tracking discount lifecycle."""

    ACTIVE = "active", _("Active")
    SCHEDULED = "scheduled", _("Scheduled")
    EXPIRED = "expired", _("Expired")
    CANCELLED = "cancelled", _("Cancelled")


# Enhanced Pydantic schemas for existing discount models
class EnhancedDiscountRulesSchema(BaseModel):
    """Enhanced Pydantic schema for discount business rules."""
    # Minimum and maximum booking values
    min_booking_value: Optional[Decimal] = Field(None, ge=0, description="Minimum booking value")
    max_booking_value: Optional[Decimal] = Field(None, ge=0, description="Maximum booking value")
    
    # Usage limits
    max_uses_per_user: Optional[int] = Field(None, ge=1, description="Maximum uses per user")
    max_total_uses: Optional[int] = Field(None, ge=1, description="Maximum total uses")
    
    # Time-based restrictions
    time_restrictions: Optional[Dict[str, Any]] = Field(None, description="Time-based restrictions")
    
    # Coupon combination rules
    can_combine_with_coupons: bool = Field(True, description="Can combine with coupon codes")
    excluded_coupon_types: Optional[List[str]] = Field(None, description="Coupon types that cannot be combined")
    
    # User restrictions
    new_users_only: bool = Field(False, description="Only for new users")
    user_type_restrictions: Optional[List[str]] = Field(None, description="User type restrictions")
    
    # Category and venue restrictions
    allowed_categories: Optional[List[int]] = Field(None, description="Allowed category IDs")
    excluded_venues: Optional[List[int]] = Field(None, description="Excluded venue IDs")
    
    @validator('max_booking_value')
    def validate_max_booking_value(cls, v, values):
        """Ensure max_booking_value is greater than min_booking_value."""
        if v is not None and 'min_booking_value' in values and values['min_booking_value'] is not None:
            if v <= values['min_booking_value']:
                raise ValueError('max_booking_value must be greater than min_booking_value')
        return v


# New Coupon-related models and enums
class CouponType(models.TextChoices):
    """Types of coupons available in the system."""
    PERCENTAGE = "percentage", _("Percentage Discount")
    FIXED_AMOUNT = "fixed_amount", _("Fixed Amount Discount")
    FREE_SHIPPING = "free_shipping", _("Free Shipping")
    BUY_X_GET_Y = "buy_x_get_y", _("Buy X Get Y Free")
    FIRST_TIME_USER = "first_time_user", _("First Time User Discount")


class CouponStatus(models.TextChoices):
    """Status options for coupons."""
    DRAFT = "draft", _("Draft")
    ACTIVE = "active", _("Active")
    PAUSED = "paused", _("Paused")
    EXPIRED = "expired", _("Expired")
    DEPLETED = "depleted", _("Depleted")


class DiscountTarget(models.TextChoices):
    """What the discount applies to."""
    ORDER_TOTAL = "order_total", _("Order Total")
    SPECIFIC_SERVICES = "specific_services", _("Specific Services")
    CATEGORY = "category", _("Category")
    VENUE = "venue", _("Venue")
    SHIPPING = "shipping", _("Shipping")


# Pydantic schemas for business rules validation
class CouponRulesSchema(BaseModel):
    """Pydantic schema for coupon business rules validation."""
    min_order_amount: Optional[Decimal] = Field(None, ge=0, description="Minimum order amount")
    max_order_amount: Optional[Decimal] = Field(None, ge=0, description="Maximum order amount")
    min_items: Optional[int] = Field(None, ge=1, description="Minimum number of items")
    max_items: Optional[int] = Field(None, ge=1, description="Maximum number of items")
    allowed_categories: Optional[List[int]] = Field(None, description="Allowed category IDs")
    excluded_categories: Optional[List[int]] = Field(None, description="Excluded category IDs")
    allowed_venues: Optional[List[int]] = Field(None, description="Allowed venue IDs")
    excluded_venues: Optional[List[int]] = Field(None, description="Excluded venue IDs")
    allowed_services: Optional[List[int]] = Field(None, description="Allowed service IDs")
    excluded_services: Optional[List[int]] = Field(None, description="Excluded service IDs")
    user_restrictions: Optional[Dict[str, Any]] = Field(None, description="User-based restrictions")
    time_restrictions: Optional[Dict[str, Any]] = Field(None, description="Time-based restrictions")
    combination_rules: Optional[Dict[str, Any]] = Field(None, description="Coupon combination rules")

    @validator('max_order_amount')
    def validate_max_order_amount(cls, v, values):
        """Ensure max_order_amount is greater than min_order_amount."""
        if v is not None and 'min_order_amount' in values and values['min_order_amount'] is not None:
            if v <= values['min_order_amount']:
                raise ValueError('max_order_amount must be greater than min_order_amount')
        return v

    @validator('max_items')
    def validate_max_items(cls, v, values):
        """Ensure max_items is greater than min_items."""
        if v is not None and 'min_items' in values and values['min_items'] is not None:
            if v <= values['min_items']:
                raise ValueError('max_items must be greater than min_items')
        return v


class PromotionConditionsSchema(BaseModel):
    """Pydantic schema for promotion conditions."""
    condition_type: str = Field(..., description="Type of condition")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Condition parameters")
    logical_operator: str = Field("AND", description="Logical operator for multiple conditions")

    @validator('condition_type')
    def validate_condition_type(cls, v):
        """Validate condition type."""
        allowed_types = [
            'min_amount', 'max_amount', 'user_type', 'first_time_user',
            'category_match', 'venue_match', 'service_match',
            'time_range', 'day_of_week', 'usage_count'
        ]
        if v not in allowed_types:
            raise ValueError(f'condition_type must be one of {allowed_types}')
        return v


class AbstractManagerDescriptor:
    """Descriptor to prevent manager access on abstract base."""

    def __get__(self, instance, owner):
        raise TypeError("Cannot instantiate abstract base class")


class DiscountBase(LifecycleModel):
    """
    Enhanced abstract base model for all discount types.
    Now includes lifecycle hooks and integration with coupon system.
    """

    def __new__(cls, *args, **kwargs):
        if cls is DiscountBase:
            raise TypeError("Cannot instantiate abstract base class")
        return super().__new__(cls)

    objects = AbstractManagerDescriptor()

    # Basic discount information
    name = models.CharField(max_length=255, help_text=_("Name/title of the discount"))
    slug = models.SlugField(
        max_length=255,
        unique=True,
        blank=True,
        help_text=_("URL-friendly version of the discount name (auto-generated)"),
    )
    description = models.TextField(
        blank=True, help_text=_("Optional description of the discount")
    )

    # Discount configuration
    discount_type = models.CharField(
        max_length=20,
        choices=DiscountType.choices,
        default=DiscountType.PERCENTAGE,
        help_text=_("Type of discount calculation"),
    )
    discount_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal("0.01"))],
        help_text=_("Discount value (percentage or fixed amount)"),
    )
    max_uses = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text=_("Maximum number of times this discount can be used"),
    )

    # Enhanced with MoneyField for better accuracy
    max_discount_amount = MoneyField(
        max_digits=10,
        decimal_places=2,
        default_currency='USD',
        null=True,
        blank=True,
        help_text="Maximum discount amount (for percentage discounts)"
    )

    # Date range
    start_date = models.DateTimeField(help_text=_("When the discount becomes active"))
    end_date = models.DateTimeField(help_text=_("When the discount expires"))

    # Enhanced business rules using Pydantic
    business_rules = SchemaField(
        schema=EnhancedDiscountRulesSchema,
        default=dict,
        help_text="Advanced business rules for discount application"
    )

    # Coupon integration
    priority = models.PositiveIntegerField(
        default=10,
        help_text="Priority for discount application (higher number = higher priority)"
    )
    is_stackable = models.BooleanField(
        default=False,
        help_text="Can this discount be combined with coupon codes?"
    )

    # Usage tracking
    current_usage_count = models.PositiveIntegerField(
        default=0,
        help_text="Current number of times this discount has been used"
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="%(class)s_created",
        help_text=_("User who created this discount"),
    )

    def _generate_unique_slug(self):
        base_slug = slugify(self.name)[:50]
        slug = base_slug
        counter = 1
        while self.__class__.objects.filter(slug=slug).exclude(pk=self.pk).exists():
            slug = f"{base_slug}-{counter}"
            counter += 1
        return slug

    @hook(BEFORE_SAVE)
    def auto_generate_slug(self):
        """Auto-generate slug before saving."""
        if not self.slug:
            self.slug = self._generate_unique_slug()

    @hook(AFTER_CREATE)
    def log_discount_creation(self):
        """Log discount creation for auditing."""
        logger.info(f"Discount created: {self.__class__.__name__} - {self.name}")

    @hook(AFTER_SAVE)
    def update_usage_count(self):
        """Update usage count after save."""
        if hasattr(self, '_skip_usage_update'):
            return
        
        Usage = apps.get_model("discount_app", "DiscountUsage")
        actual_count = Usage.objects.filter(
            discount_type=self.__class__.__name__,
            discount_id=self.id,
        ).count()
        
        if actual_count != self.current_usage_count:
            self._skip_usage_update = True
            self.current_usage_count = actual_count
            self.save(update_fields=['current_usage_count'])
            delattr(self, '_skip_usage_update')

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = self._generate_unique_slug()
        super().save(*args, **kwargs)

    class Meta:
        abstract = True

    def __str__(self):
        return self.name

    def clean(self):
        """Enhanced validation for discount models."""
        super().clean()

        # Validate date range
        if self.start_date and self.end_date and self.start_date >= self.end_date:
            raise ValidationError(_("Start date must be before end date"))

        # Validate discount value based on type
        if self.discount_value is not None:
            if self.discount_type == DiscountType.PERCENTAGE:
                if self.discount_value <= 0 or self.discount_value > 100:
                    raise ValidationError(_("Percentage must be between 0.01 and 100"))
                if self.discount_value > 80:  # Max 80% as per requirements
                    raise ValidationError(_("Percentage discount cannot exceed 80%"))
            elif self.discount_type == DiscountType.FIXED_AMOUNT:
                if self.discount_value <= 0:
                    raise ValidationError(_("Fixed amount must be greater than 0"))

        # Validate business rules
        if self.business_rules:
            try:
                # Validate the schema
                EnhancedDiscountRulesSchema(**self.business_rules)
            except Exception as e:
                raise ValidationError(f"Invalid business rules: {str(e)}")

    def is_active(self):
        """Check if the discount is currently active."""
        now = timezone.now()
        return self.start_date <= now <= self.end_date

    def get_status(self):
        """Get the current status of the discount."""
        now = timezone.now()
        if self.start_date > now:
            return DiscountStatus.SCHEDULED
        elif self.end_date < now:
            return DiscountStatus.EXPIRED
        else:
            return DiscountStatus.ACTIVE

    def calculate_discount_amount(self, original_price, apply_max_cap=True):
        """Enhanced discount calculation with MoneyField support."""
        if self.discount_type == DiscountType.PERCENTAGE:
            # Ensure discount_value is between 0 and 80 for percentage
            percentage = min(max(self.discount_value, 0), 80)
            discount_amount = (original_price * percentage) / 100
            
            # Apply maximum discount cap if specified
            if apply_max_cap and self.max_discount_amount:
                if isinstance(original_price, Money):
                    max_cap = self.max_discount_amount.amount
                else:
                    max_cap = self.max_discount_amount.amount
                discount_amount = min(discount_amount, max_cap)
            
            return discount_amount
        else:  # Fixed amount
            # Ensure discount doesn't exceed the original price
            discount_value = self.discount_value
            if isinstance(original_price, Money):
                return min(discount_value, original_price.amount)
            return min(discount_value, original_price)

    def can_be_combined_with_coupon(self, coupon_code=None):
        """Check if this discount can be combined with a coupon."""
        if not self.is_stackable:
            return False
            
        if not self.business_rules.get('can_combine_with_coupons', True):
            return False
            
        # Check excluded coupon types
        if coupon_code and self.business_rules.get('excluded_coupon_types'):
            excluded_types = self.business_rules['excluded_coupon_types']
            if coupon_code.coupon_type in excluded_types:
                return False
                
        return True

    def is_eligible_for_user(self, user):
        """Enhanced user eligibility check."""
        if not self.business_rules:
            return True
            
        # Check if new users only
        if self.business_rules.get('new_users_only', False):
            # You would implement logic to check if user is new
            # For now, assume all users are eligible
            pass
            
        # Check user type restrictions
        user_type_restrictions = self.business_rules.get('user_type_restrictions')
        if user_type_restrictions:
            # You would implement logic to check user type
            # For now, assume all users are eligible
            pass
            
        # Check max uses per user
        max_uses_per_user = self.business_rules.get('max_uses_per_user')
        if max_uses_per_user:
            Usage = apps.get_model("discount_app", "DiscountUsage")
            user_usage_count = Usage.objects.filter(
                discount_type=self.__class__.__name__,
                discount_id=self.id,
                user=user
            ).count()
            
            if user_usage_count >= max_uses_per_user:
                return False
                
        return True

    def is_eligible_for_booking(self, booking_total, categories=None, venues=None):
        """Enhanced booking eligibility check."""
        if not self.business_rules:
            return True
            
        # Check minimum booking value
        min_booking_value = self.business_rules.get('min_booking_value')
        if min_booking_value and booking_total < min_booking_value:
            return False
            
        # Check maximum booking value
        max_booking_value = self.business_rules.get('max_booking_value')
        if max_booking_value and booking_total > max_booking_value:
            return False
            
        # Check category restrictions
        allowed_categories = self.business_rules.get('allowed_categories')
        if allowed_categories and categories:
            if not any(cat_id in allowed_categories for cat_id in categories):
                return False
                
        # Check venue restrictions
        excluded_venues = self.business_rules.get('excluded_venues')
        if excluded_venues and venues:
            if any(venue_id in excluded_venues for venue_id in venues):
                return False
                
        return True

    def get_usage_count(self):
        """Get current usage count."""
        return self.current_usage_count

    def remaining_uses(self):
        if self.max_uses is None:
            return None
        return max(self.max_uses - self.get_usage_count(), 0)

    def usage_limit_reached(self):
        remaining = self.remaining_uses()
        return remaining is not None and remaining <= 0

    def calculate_discounted_price(self, original_price):
        """Calculate the final price after applying the discount."""
        discount_amount = self.calculate_discount_amount(original_price)
        if isinstance(original_price, Money):
            return original_price - Money(discount_amount, original_price.currency)
        return max(original_price - discount_amount, Decimal("0.00"))

    def increment_usage(self):
        """Increment usage count."""
        self.current_usage_count += 1
        self.save(update_fields=['current_usage_count'])


class VenueDiscount(DiscountBase):
    """
    Discount applied to an entire venue.
    Created by service providers for their venues.
    """

    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name="discounts",
        help_text=_("Venue this discount applies to"),
    )

    # Minimum booking requirements
    min_booking_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal("0.00"),
        validators=[MinValueValidator(Decimal("0.00"))],
        help_text=_("Minimum booking value required to apply this discount"),
    )

    # Maximum discount cap for percentage discounts
    max_discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal("0.01"))],
        help_text=_("Maximum discount amount for percentage discounts (optional)"),
    )

    # Admin approval workflow
    is_approved = models.BooleanField(
        default=False, help_text=_("Whether this discount has been approved by admin")
    )
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="approved_venue_discounts",
        help_text=_("Admin who approved this discount"),
    )
    approved_at = models.DateTimeField(
        null=True, blank=True, help_text=_("When this discount was approved")
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = _("Venue Discount")
        verbose_name_plural = _("Venue Discounts")

    def clean(self):
        """Custom validation for venue discounts."""
        super().clean()

        # Ensure the user creating the discount owns the venue
        if self.created_by and hasattr(self.created_by, "service_provider_profile"):
            if self.venue.service_provider != self.created_by.service_provider_profile:
                raise ValidationError(
                    _("You can only create discounts for your own venue")
                )

    def save(self, *args, **kwargs):
        """Override save to handle approval timestamp."""
        # Set approved_at when status changes to approved
        if self.is_approved and not self.approved_at:
            self.approved_at = timezone.now()
        elif not self.is_approved:
            self.approved_at = None
            self.approved_by = None

        super().save(*args, **kwargs)

    @property
    def is_visible(self):
        """Check if discount is visible to customers."""
        return self.is_approved and self.is_active()


class ServiceDiscount(DiscountBase):
    """
    Discount applied to a specific service.
    Created by service providers for individual services.
    """

    service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE,
        related_name="discounts",
        help_text=_("Service this discount applies to"),
    )

    # Admin approval workflow
    is_approved = models.BooleanField(
        default=False, help_text=_("Whether this discount has been approved by admin")
    )
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="approved_service_discounts",
        help_text=_("Admin who approved this discount"),
    )
    approved_at = models.DateTimeField(
        null=True, blank=True, help_text=_("When this discount was approved")
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = _("Service Discount")
        verbose_name_plural = _("Service Discounts")

    def clean(self):
        """Custom validation for service discounts."""
        super().clean()

        # Ensure the user creating the discount owns the service's venue
        if self.created_by and hasattr(self.created_by, "service_provider_profile"):
            if (
                self.service.venue.service_provider
                != self.created_by.service_provider_profile
            ):
                raise ValidationError(
                    _("You can only create discounts for services in your own venue")
                )

    def save(self, *args, **kwargs):
        """Override save to handle approval timestamp."""
        # Set approved_at when status changes to approved
        if self.is_approved and not self.approved_at:
            self.approved_at = timezone.now()
        elif not self.is_approved:
            self.approved_at = None
            self.approved_by = None

        super().save(*args, **kwargs)

    @property
    def is_visible(self):
        """Check if discount is visible to customers."""
        return self.is_approved and self.is_active()

    def get_discounted_service_price(self):
        """Calculate the discounted price for the service."""
        return self.calculate_discounted_price(self.service.price_min)


class PlatformDiscount(DiscountBase):
    """
    Platform-wide discount created by administrators.
    Can be applied across multiple venues and categories.
    """

    # Category filtering (optional)
    category = models.ForeignKey(
        Category,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="platform_discounts",
        help_text=_(
            "Category this discount applies to (optional - leave blank for all categories)"
        ),
    )

    # Minimum booking requirements
    min_booking_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal("0.00"),
        validators=[MinValueValidator(Decimal("0.00"))],
        help_text=_("Minimum booking value required to apply this discount"),
    )

    # Maximum discount cap for percentage discounts
    max_discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal("0.01"))],
        help_text=_("Maximum discount amount for percentage discounts (optional)"),
    )

    # Featured status for homepage display
    is_featured = models.BooleanField(
        default=False,
        help_text=_("Whether this discount should be featured on the homepage"),
    )

    class Meta:
        ordering = ["-created_at"]
        verbose_name = _("Platform Discount")
        verbose_name_plural = _("Platform Discounts")

    def clean(self):
        """Custom validation for platform discounts."""
        # Call parent clean but skip the discount_value validation
        models.Model.clean(self)

        # Validate date range
        if self.start_date and self.end_date and self.start_date >= self.end_date:
            raise ValidationError(_("Start date must be before end date"))

        # Validate discount value based on type (platform allows up to 100%)
        if self.discount_value is not None:
            if self.discount_type == DiscountType.PERCENTAGE:
                if self.discount_value <= 0 or self.discount_value > 100:
                    raise ValidationError(_("Percentage must be between 0.01 and 100"))
            elif self.discount_type == DiscountType.FIXED_AMOUNT:
                if self.discount_value <= 0:
                    raise ValidationError(_("Fixed amount must be greater than 0"))

    @property
    def is_visible(self):
        """Check if discount is visible to customers."""
        return self.is_active()


class DiscountUsage(models.Model):
    """
    Track discount usage by customers for analytics and preventing abuse.
    Uses generic foreign key pattern to track usage across different discount types.
    """

    DISCOUNT_MODEL_CHOICES = [
        ("VenueDiscount", _("Venue Discount")),
        ("ServiceDiscount", _("Service Discount")),
        ("PlatformDiscount", _("Platform Discount")),
        ("CouponCode", _("Coupon Code")),
    ]

    # User who used the discount
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="discount_usages",
        help_text=_("Customer who used this discount"),
    )

    # Generic reference to the discount used
    discount_type = models.CharField(
        max_length=20,
        choices=DISCOUNT_MODEL_CHOICES,
        help_text=_("Type of discount that was used"),
    )
    discount_id = models.PositiveIntegerField(
        help_text=_("ID of the specific discount that was used")
    )

    # Booking reference (will be linked when booking_cart_app is implemented)
    booking_reference = models.CharField(
        max_length=100,
        help_text=_("Reference to the booking where this discount was applied"),
    )

    # Financial details
    original_price = models.DecimalField(
        max_digits=10, decimal_places=2, help_text=_("Original price before discount")
    )
    discount_amount = models.DecimalField(
        max_digits=10, decimal_places=2, help_text=_("Amount of discount applied")
    )
    final_price = models.DecimalField(
        max_digits=10, decimal_places=2, help_text=_("Final price after discount")
    )

    # Metadata
    used_at = models.DateTimeField(
        auto_now_add=True, help_text=_("When the discount was used")
    )
    ip_address = models.GenericIPAddressField(
        null=True, blank=True, help_text=_("IP address of the user")
    )

    class Meta:
        ordering = ["-used_at"]
        verbose_name = _("Discount Usage")
        verbose_name_plural = _("Discount Usages")
        indexes = [
            models.Index(fields=["discount_type", "discount_id"]),
            models.Index(fields=["user", "-used_at"]),
            models.Index(fields=["-used_at"]),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(original_price__gte=0),
                name="positive_original_price",
            ),
            models.CheckConstraint(
                check=models.Q(discount_amount__gte=0),
                name="positive_discount_amount",
            ),
            models.CheckConstraint(
                check=models.Q(final_price__gte=0), name="positive_final_price"
            ),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.discount_type} - {self.used_at.strftime('%Y-%m-%d')}"

    def get_discount_object(self):
        """Get the actual discount object that was used."""
        model_map = {
            "VenueDiscount": VenueDiscount,
            "ServiceDiscount": ServiceDiscount,
            "PlatformDiscount": PlatformDiscount,
        }
        
        model_class = model_map.get(self.discount_type)
        if model_class:
            try:
                return model_class.objects.get(id=self.discount_id)
            except model_class.DoesNotExist:
                return None
        return None

    def get_savings_amount(self):
        """Calculate the amount saved by using this discount."""
        return self.original_price - self.final_price

    def get_savings_percentage(self):
        """Calculate the percentage saved by using this discount."""
        if self.original_price > 0:
            return round((self.get_savings_amount() / self.original_price) * 100, 2)
        return 0


# ===== NEW COUPON MODELS =====

class CouponCode(LifecycleModel):
    """Modern coupon code model with enhanced features."""

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    code = models.CharField(
        max_length=50,
        unique=True,
        help_text="Unique coupon code (auto-generated if not provided)"
    )
    name = models.CharField(
        max_length=255,
        help_text="Internal name for the coupon"
    )
    description = models.TextField(
        blank=True,
        help_text="Description of what this coupon offers"
    )

    # Coupon Configuration
    coupon_type = models.CharField(
        max_length=20,
        choices=CouponType.choices,
        default=CouponType.PERCENTAGE,
        help_text="Type of discount this coupon provides"
    )
    status = models.CharField(
        max_length=20,
        choices=CouponStatus.choices,
        default=CouponStatus.DRAFT,
        help_text="Current status of the coupon"
    )
    target = models.CharField(
        max_length=20,
        choices=DiscountTarget.choices,
        default=DiscountTarget.ORDER_TOTAL,
        help_text="What this discount applies to"
    )

    # Discount Values (using MoneyField for precision)
    discount_value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal("0.01"))],
        help_text="Discount value (percentage or amount)"
    )
    max_discount_amount = MoneyField(
        max_digits=10,
        decimal_places=2,
        default_currency='USD',
        null=True,
        blank=True,
        help_text="Maximum discount amount (for percentage coupons)"
    )

    # Usage Limits
    usage_limit = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Maximum number of times this coupon can be used"
    )
    usage_limit_per_user = models.PositiveIntegerField(
        default=1,
        help_text="Maximum uses per user"
    )
    current_usage_count = models.PositiveIntegerField(
        default=0,
        help_text="Current number of times this coupon has been used"
    )

    # Date/Time Restrictions
    valid_from = models.DateTimeField(
        help_text="When this coupon becomes valid"
    )
    valid_until = models.DateTimeField(
        help_text="When this coupon expires"
    )

    # Business Rules (using Pydantic schema)
    business_rules = SchemaField(schema=CouponRulesSchema)

    # Relationships
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_coupons",
        help_text="User who created this coupon"
    )
    allowed_users = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name="allowed_coupons",
        help_text="Specific users who can use this coupon (empty = all users)"
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_stackable = models.BooleanField(
        default=False,
        help_text="Can this coupon be combined with other discounts?"
    )
    priority = models.PositiveIntegerField(
        default=10,
        help_text="Priority for coupon application (higher number = higher priority)"
    )

    class Meta:
        ordering = ['-priority', '-created_at']
        verbose_name = "Coupon Code"
        verbose_name_plural = "Coupon Codes"
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['status', 'valid_from', 'valid_until']),
            models.Index(fields=['-created_at']),
        ]

    def __str__(self):
        return f"{self.code} - {self.name}"

    @hook(BEFORE_SAVE)
    def generate_code_if_empty(self):
        """Generate a unique code if not provided."""
        if not self.code:
            self.code = self.generate_unique_code()

    @hook(AFTER_CREATE)
    def log_coupon_creation(self):
        """Log coupon creation for auditing."""
        logger.info(f"Coupon created: {self.code} - {self.name}")

    def clean(self):
        """Validate coupon data."""
        super().clean()

        # Validate date range
        if self.valid_from and self.valid_until:
            if self.valid_from >= self.valid_until:
                raise ValidationError("Valid from date must be before valid until date")

        # Validate discount value
        if self.discount_value is not None:
            if self.coupon_type == CouponType.PERCENTAGE:
                if self.discount_value <= 0 or self.discount_value > 100:
                    raise ValidationError("Percentage must be between 0.01 and 100")
            elif self.coupon_type == CouponType.FIXED_AMOUNT:
                if self.discount_value <= 0:
                    raise ValidationError("Fixed amount must be greater than 0")

        # Validate usage limits
        if self.usage_limit_per_user and self.usage_limit:
            if self.usage_limit_per_user > self.usage_limit:
                raise ValidationError("Usage limit per user cannot exceed total usage limit")

    @staticmethod
    def generate_unique_code(length=8, prefix="COZY"):
        """Generate a unique coupon code."""
        import string
        import secrets
        
        chars = string.ascii_uppercase + string.digits
        chars = chars.replace('0', '').replace('O', '').replace('1', '').replace('I', '')  # Remove confusing chars
        
        while True:
            code_part = ''.join(secrets.choice(chars) for _ in range(length))
            code = f"{prefix}{code_part}" if prefix else code_part
            
            if not CouponCode.objects.filter(code=code).exists():
                return code

    def is_valid(self, user=None, cart_total=None):
        """Check if the coupon is valid for use."""
        now = timezone.now()
        
        # Check status
        if self.status != CouponStatus.ACTIVE:
            return False
        
        # Check date range
        if now < self.valid_from or now > self.valid_until:
            return False
        
        # Check usage limits
        if self.usage_limit and self.current_usage_count >= self.usage_limit:
            return False
        
        # Check user-specific limits
        if user and self.usage_limit_per_user:
            user_usage = CouponUsage.objects.filter(coupon=self, user=user).count()
            if user_usage >= self.usage_limit_per_user:
                return False
        
        # Check if user is in allowed users (if specified)
        if user and self.allowed_users.exists():
            if not self.allowed_users.filter(id=user.id).exists():
                return False
        
        return True

    def calculate_discount(self, order_total, items=None):
        """Calculate the discount amount for this coupon."""
        if self.coupon_type == CouponType.PERCENTAGE:
            discount_amount = (order_total * self.discount_value) / 100
            
            # Apply maximum discount cap if specified
            if self.max_discount_amount:
                discount_amount = min(discount_amount, self.max_discount_amount.amount)
            
            return discount_amount
        elif self.coupon_type == CouponType.FIXED_AMOUNT:
            return min(self.discount_value, order_total)
        
        # For other coupon types, implement specific logic
        return Decimal('0')

    def increment_usage(self):
        """Increment the usage count."""
        self.current_usage_count += 1
        self.save(update_fields=['current_usage_count'])


class CouponUsage(models.Model):
    """Track individual coupon usage instances."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    coupon = models.ForeignKey(
        CouponCode,
        on_delete=models.CASCADE,
        related_name="usage_instances"
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="coupon_usage_history"
    )
    
    # Order information (will be linked when booking system is implemented)
    order_reference = models.CharField(
        max_length=100,
        help_text="Reference to the order where this coupon was used"
    )
    
    # Financial details
    original_amount = MoneyField(
        max_digits=10,
        decimal_places=2,
        default_currency='USD',
        help_text="Original order amount before discount"
    )
    discount_amount = MoneyField(
        max_digits=10,
        decimal_places=2,
        default_currency='USD',
        help_text="Amount of discount applied"
    )
    final_amount = MoneyField(
        max_digits=10,
        decimal_places=2,
        default_currency='USD',
        help_text="Final order amount after discount"
    )
    
    # Metadata
    used_at = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    class Meta:
        ordering = ['-used_at']
        verbose_name = "Coupon Usage"
        verbose_name_plural = "Coupon Usage Records"
        indexes = [
            models.Index(fields=['coupon', '-used_at']),
            models.Index(fields=['user', '-used_at']),
            models.Index(fields=['-used_at']),
        ]

    def __str__(self):
        return f"{self.user.username} used {self.coupon.code} on {self.used_at.date()}"


class PromotionCampaign(LifecycleModel):
    """Advanced promotion campaign with complex business rules."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, help_text="Campaign name")
    slug = models.SlugField(max_length=255, unique=True, help_text="URL-friendly campaign identifier")
    description = models.TextField(help_text="Campaign description")
    
    # Campaign status and timing
    status = models.CharField(
        max_length=20,
        choices=CouponStatus.choices,
        default=CouponStatus.DRAFT
    )
    start_date = models.DateTimeField(help_text="Campaign start date")
    end_date = models.DateTimeField(help_text="Campaign end date")
    
    # Promotion rules (using Pydantic schema)
    conditions = SchemaField(schema=List[PromotionConditionsSchema])
    
    # Associated coupons
    coupons = models.ManyToManyField(
        CouponCode,
        blank=True,
        related_name="campaigns",
        help_text="Coupons associated with this campaign"
    )
    
    # Campaign metrics
    target_users = models.PositiveIntegerField(
        null=True, blank=True,
        help_text="Target number of users for this campaign"
    )
    budget_limit = MoneyField(
        max_digits=12,
        decimal_places=2,
        default_currency='USD',
        null=True, blank=True,
        help_text="Maximum budget for this campaign"
    )
    current_spend = MoneyField(
        max_digits=12,
        decimal_places=2,
        default_currency='USD',
        default=Money(0, 'USD'),
        help_text="Current amount spent on this campaign"
    )
    
    # Metadata
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_campaigns"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Promotion Campaign"
        verbose_name_plural = "Promotion Campaigns"

    def __str__(self):
        return self.name

    @hook(AFTER_CREATE)
    def log_campaign_creation(self):
        """Log campaign creation for auditing."""
        logger.info(f"Campaign created: {self.name}")

    def save(self, *args, **kwargs):
        """Auto-generate slug if not provided."""
        if not self.slug:
            self.slug = slugify(self.name)[:255]
            
            # Ensure uniqueness
            counter = 1
            original_slug = self.slug
            while PromotionCampaign.objects.filter(slug=self.slug).exclude(pk=self.pk).exists():
                self.slug = f"{original_slug}-{counter}"
                counter += 1
        
        super().save(*args, **kwargs)

    def is_active(self):
        """Check if the campaign is currently active."""
        now = timezone.now()
        return (
            self.status == CouponStatus.ACTIVE and
            self.start_date <= now <= self.end_date
        )

    def budget_remaining(self):
        """Calculate remaining budget."""
        if self.budget_limit:
            return self.budget_limit - self.current_spend
        return None

    def is_budget_available(self, amount):
        """Check if budget is available for the specified amount."""
        remaining = self.budget_remaining()
        if remaining is None:
            return True  # No budget limit
        return remaining >= Money(amount, 'USD')
