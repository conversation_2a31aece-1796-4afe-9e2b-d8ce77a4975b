"""
Advanced Pydantic schemas for CozyWish discount system.
These schemas provide comprehensive validation for discount rules, coupon codes,
and business logic integration.
"""

from decimal import Decimal
from datetime import datetime, time
from typing import Optional, List, Dict, Any, Union, Literal
from enum import Enum

from pydantic import BaseModel, Field, validator, root_validator
from pydantic.types import StrictStr, StrictInt, StrictBool


class DiscountTypeEnum(str, Enum):
    """Discount type enumeration."""
    PERCENTAGE = "percentage"
    FIXED_AMOUNT = "fixed_amount"


class CouponTypeEnum(str, Enum):
    """Coupon type enumeration."""
    PERCENTAGE = "percentage"
    FIXED_AMOUNT = "fixed_amount"
    FREE_SHIPPING = "free_shipping"
    BUY_X_GET_Y = "buy_x_get_y"
    FIRST_TIME_USER = "first_time_user"


class UserTypeEnum(str, Enum):
    """User type enumeration for restrictions."""
    NEW_USER = "new_user"
    RETURNING_USER = "returning_user"
    PREMIUM_USER = "premium_user"
    BUSINESS_USER = "business_user"


class TimeRestrictionSchema(BaseModel):
    """Schema for time-based restrictions."""
    start_time: Optional[time] = Field(None, description="Start time of day")
    end_time: Optional[time] = Field(None, description="End time of day")
    days_of_week: Optional[List[int]] = Field(None, description="Days of week (0=Monday, 6=Sunday)")
    specific_dates: Optional[List[datetime]] = Field(None, description="Specific dates when discount applies")
    exclude_dates: Optional[List[datetime]] = Field(None, description="Specific dates to exclude")
    
    @validator('days_of_week')
    def validate_days_of_week(cls, v):
        """Validate days of week are in valid range."""
        if v is not None:
            for day in v:
                if day < 0 or day > 6:
                    raise ValueError("Days of week must be between 0 (Monday) and 6 (Sunday)")
        return v
    
    @validator('end_time')
    def validate_time_range(cls, v, values):
        """Ensure end_time is after start_time."""
        if v is not None and 'start_time' in values and values['start_time'] is not None:
            if v <= values['start_time']:
                raise ValueError("End time must be after start time")
        return v


class CombinationRulesSchema(BaseModel):
    """Schema for discount combination rules."""
    can_combine_with_coupons: StrictBool = Field(True, description="Can combine with coupon codes")
    can_combine_with_discounts: StrictBool = Field(False, description="Can combine with other discounts")
    excluded_coupon_types: Optional[List[CouponTypeEnum]] = Field(None, description="Excluded coupon types")
    excluded_discount_types: Optional[List[DiscountTypeEnum]] = Field(None, description="Excluded discount types")
    max_total_discount_percentage: Optional[Decimal] = Field(None, ge=0, le=100, description="Maximum total discount percentage")
    priority: StrictInt = Field(10, ge=1, le=100, description="Discount priority (higher = more important)")


class UserEligibilitySchema(BaseModel):
    """Schema for user eligibility rules."""
    new_users_only: StrictBool = Field(False, description="Only for new users")
    user_types: Optional[List[UserTypeEnum]] = Field(None, description="Allowed user types")
    min_account_age_days: Optional[StrictInt] = Field(None, ge=0, description="Minimum account age in days")
    max_account_age_days: Optional[StrictInt] = Field(None, ge=0, description="Maximum account age in days")
    min_previous_bookings: Optional[StrictInt] = Field(None, ge=0, description="Minimum number of previous bookings")
    max_previous_bookings: Optional[StrictInt] = Field(None, ge=0, description="Maximum number of previous bookings")
    excluded_user_ids: Optional[List[int]] = Field(None, description="Excluded user IDs")
    
    @validator('max_account_age_days')
    def validate_account_age_range(cls, v, values):
        """Ensure max_account_age_days is greater than min_account_age_days."""
        if v is not None and 'min_account_age_days' in values and values['min_account_age_days'] is not None:
            if v <= values['min_account_age_days']:
                raise ValueError("Maximum account age must be greater than minimum account age")
        return v
    
    @validator('max_previous_bookings')
    def validate_booking_range(cls, v, values):
        """Ensure max_previous_bookings is greater than min_previous_bookings."""
        if v is not None and 'min_previous_bookings' in values and values['min_previous_bookings'] is not None:
            if v <= values['min_previous_bookings']:
                raise ValueError("Maximum previous bookings must be greater than minimum previous bookings")
        return v


class BookingEligibilitySchema(BaseModel):
    """Schema for booking eligibility rules."""
    min_booking_value: Optional[Decimal] = Field(None, ge=0, description="Minimum booking value")
    max_booking_value: Optional[Decimal] = Field(None, ge=0, description="Maximum booking value")
    min_items: Optional[StrictInt] = Field(None, ge=1, description="Minimum number of items")
    max_items: Optional[StrictInt] = Field(None, ge=1, description="Maximum number of items")
    allowed_categories: Optional[List[int]] = Field(None, description="Allowed category IDs")
    excluded_categories: Optional[List[int]] = Field(None, description="Excluded category IDs")
    allowed_venues: Optional[List[int]] = Field(None, description="Allowed venue IDs")
    excluded_venues: Optional[List[int]] = Field(None, description="Excluded venue IDs")
    allowed_services: Optional[List[int]] = Field(None, description="Allowed service IDs")
    excluded_services: Optional[List[int]] = Field(None, description="Excluded service IDs")
    
    @validator('max_booking_value')
    def validate_booking_value_range(cls, v, values):
        """Ensure max_booking_value is greater than min_booking_value."""
        if v is not None and 'min_booking_value' in values and values['min_booking_value'] is not None:
            if v <= values['min_booking_value']:
                raise ValueError("Maximum booking value must be greater than minimum booking value")
        return v
    
    @validator('max_items')
    def validate_items_range(cls, v, values):
        """Ensure max_items is greater than min_items."""
        if v is not None and 'min_items' in values and values['min_items'] is not None:
            if v <= values['min_items']:
                raise ValueError("Maximum items must be greater than minimum items")
        return v
    
    @validator('excluded_categories', always=True)
    def validate_category_venue_conflicts(cls, v, values):
        """Ensure no conflicts between allowed/excluded categories and venues."""
        allowed_categories = values.get('allowed_categories', [])
        excluded_categories = v or []
        
        if allowed_categories and excluded_categories:
            overlap = set(allowed_categories) & set(excluded_categories)
            if overlap:
                raise ValueError(f"Categories cannot be both allowed and excluded: {overlap}")
        
        return v


class UsageLimitSchema(BaseModel):
    """Schema for usage limit rules."""
    max_uses_per_user: Optional[StrictInt] = Field(None, ge=1, description="Maximum uses per user")
    max_total_uses: Optional[StrictInt] = Field(None, ge=1, description="Maximum total uses")
    usage_period_days: Optional[StrictInt] = Field(None, ge=1, description="Usage period in days")
    cooldown_period_hours: Optional[StrictInt] = Field(None, ge=1, description="Cooldown period between uses")
    
    @root_validator
    def validate_usage_limits(cls, values):
        """Validate usage limit relationships."""
        max_per_user = values.get('max_uses_per_user')
        max_total = values.get('max_total_uses')
        
        if max_per_user is not None and max_total is not None:
            if max_per_user > max_total:
                raise ValueError("Max uses per user cannot exceed max total uses")
        
        return values


class ComprehensiveDiscountRulesSchema(BaseModel):
    """Comprehensive schema for all discount business rules."""
    # Core eligibility rules
    user_eligibility: Optional[UserEligibilitySchema] = Field(None, description="User eligibility rules")
    booking_eligibility: Optional[BookingEligibilitySchema] = Field(None, description="Booking eligibility rules")
    time_restrictions: Optional[TimeRestrictionSchema] = Field(None, description="Time-based restrictions")
    
    # Usage and combination rules
    usage_limits: Optional[UsageLimitSchema] = Field(None, description="Usage limit rules")
    combination_rules: Optional[CombinationRulesSchema] = Field(None, description="Combination rules")
    
    # Advanced features
    requires_approval: StrictBool = Field(False, description="Requires admin approval")
    auto_apply: StrictBool = Field(False, description="Auto-apply if eligible")
    show_in_listings: StrictBool = Field(True, description="Show in public listings")
    
    # Tracking and analytics
    track_usage: StrictBool = Field(True, description="Track usage for analytics")
    send_notifications: StrictBool = Field(False, description="Send notifications when used")


class CouponValidationSchema(BaseModel):
    """Schema for coupon code validation."""
    code: StrictStr = Field(..., min_length=3, max_length=50, description="Coupon code")
    user_id: Optional[int] = Field(None, description="User ID for validation")
    booking_total: Optional[Decimal] = Field(None, ge=0, description="Booking total for validation")
    category_ids: Optional[List[int]] = Field(None, description="Category IDs in booking")
    venue_ids: Optional[List[int]] = Field(None, description="Venue IDs in booking")
    service_ids: Optional[List[int]] = Field(None, description="Service IDs in booking")
    
    @validator('code')
    def validate_code_format(cls, v):
        """Validate coupon code format."""
        if not v.isalnum():
            raise ValueError("Coupon code must contain only alphanumeric characters")
        return v.upper()


class DiscountCalculationSchema(BaseModel):
    """Schema for discount calculation results."""
    discount_type: DiscountTypeEnum = Field(..., description="Type of discount")
    original_amount: Decimal = Field(..., ge=0, description="Original amount")
    discount_amount: Decimal = Field(..., ge=0, description="Discount amount")
    final_amount: Decimal = Field(..., ge=0, description="Final amount after discount")
    discount_percentage: Optional[Decimal] = Field(None, ge=0, le=100, description="Discount as percentage")
    applied_rules: List[str] = Field(default_factory=list, description="Applied business rules")
    
    @root_validator
    def validate_calculation(cls, values):
        """Validate discount calculation consistency."""
        original = values.get('original_amount')
        discount = values.get('discount_amount')
        final = values.get('final_amount')
        
        if original is not None and discount is not None and final is not None:
            if abs((original - discount) - final) > Decimal('0.01'):
                raise ValueError("Discount calculation is inconsistent")
        
        return values


class CouponGenerationSchema(BaseModel):
    """Schema for coupon code generation."""
    prefix: Optional[StrictStr] = Field("COZY", max_length=10, description="Coupon code prefix")
    length: StrictInt = Field(8, ge=4, le=20, description="Length of generated code")
    quantity: StrictInt = Field(1, ge=1, le=1000, description="Number of codes to generate")
    exclude_ambiguous: StrictBool = Field(True, description="Exclude ambiguous characters (0, O, 1, I)")
    
    @validator('prefix')
    def validate_prefix(cls, v):
        """Validate prefix format."""
        if v and not v.isalnum():
            raise ValueError("Prefix must contain only alphanumeric characters")
        return v.upper() if v else v


class PromotionCampaignSchema(BaseModel):
    """Schema for promotion campaign validation."""
    name: StrictStr = Field(..., min_length=1, max_length=255, description="Campaign name")
    description: Optional[StrictStr] = Field(None, description="Campaign description")
    start_date: datetime = Field(..., description="Campaign start date")
    end_date: datetime = Field(..., description="Campaign end date")
    budget_limit: Optional[Decimal] = Field(None, ge=0, description="Budget limit")
    target_users: Optional[StrictInt] = Field(None, ge=1, description="Target number of users")
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        """Ensure end_date is after start_date."""
        if 'start_date' in values and values['start_date'] is not None:
            if v <= values['start_date']:
                raise ValueError("End date must be after start date")
        return v


# Export all schemas
__all__ = [
    'DiscountTypeEnum',
    'CouponTypeEnum',
    'UserTypeEnum',
    'TimeRestrictionSchema',
    'CombinationRulesSchema',
    'UserEligibilitySchema',
    'BookingEligibilitySchema',
    'UsageLimitSchema',
    'ComprehensiveDiscountRulesSchema',
    'CouponValidationSchema',
    'DiscountCalculationSchema',
    'CouponGenerationSchema',
    'PromotionCampaignSchema',
] 