"""
Management command to generate multiple coupon codes.

Usage:
    python manage.py generate_coupons --count 100 --type percentage --value 10 --prefix SAVE
    python manage.py generate_coupons --count 50 --type fixed_amount --value 25 --campaign "Black Friday"
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal
import logging

from discount_app.models import CouponCode, CouponType, CouponStatus, PromotionCampaign


logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Generate multiple coupon codes with specified parameters'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='Number of coupons to generate (default: 10)'
        )
        parser.add_argument(
            '--type',
            type=str,
            choices=['percentage', 'fixed_amount', 'free_shipping', 'buy_x_get_y', 'first_time_user'],
            default='percentage',
            help='Type of discount coupon (default: percentage)'
        )
        parser.add_argument(
            '--value',
            type=float,
            required=True,
            help='Discount value (percentage or amount)'
        )
        parser.add_argument(
            '--prefix',
            type=str,
            default='COZY',
            help='Prefix for coupon codes (default: COZY)'
        )
        parser.add_argument(
            '--name-prefix',
            type=str,
            default='Generated Coupon',
            help='Prefix for coupon names (default: Generated Coupon)'
        )
        parser.add_argument(
            '--days-valid',
            type=int,
            default=30,
            help='Number of days the coupon is valid (default: 30)'
        )
        parser.add_argument(
            '--usage-limit',
            type=int,
            help='Maximum number of times each coupon can be used'
        )
        parser.add_argument(
            '--usage-limit-per-user',
            type=int,
            default=1,
            help='Maximum uses per user (default: 1)'
        )
        parser.add_argument(
            '--max-discount',
            type=float,
            help='Maximum discount amount (for percentage coupons)'
        )
        parser.add_argument(
            '--min-order',
            type=float,
            help='Minimum order amount required'
        )
        parser.add_argument(
            '--campaign',
            type=str,
            help='Associate coupons with a specific campaign slug'
        )
        parser.add_argument(
            '--status',
            type=str,
            choices=['draft', 'active', 'paused'],
            default='active',
            help='Status of generated coupons (default: active)'
        )
        parser.add_argument(
            '--priority',
            type=int,
            default=10,
            help='Priority for coupon application (default: 10)'
        )
        parser.add_argument(
            '--stackable',
            action='store_true',
            help='Allow coupon to be combined with other discounts'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating coupons'
        )

    def handle(self, *args, **options):
        """Handle the command execution."""
        count = options['count']
        coupon_type = options['type']
        value = Decimal(str(options['value']))
        prefix = options['prefix']
        name_prefix = options['name_prefix']
        days_valid = options['days_valid']
        usage_limit = options['usage_limit']
        usage_limit_per_user = options['usage_limit_per_user']
        max_discount = options['max_discount']
        min_order = options['min_order']
        campaign_slug = options['campaign']
        status = options['status']
        priority = options['priority']
        stackable = options['stackable']
        dry_run = options['dry_run']

        # Validate inputs
        if value <= 0:
            raise CommandError("Discount value must be positive")

        if coupon_type == 'percentage' and value > 100:
            raise CommandError("Percentage discount cannot exceed 100%")

        if count <= 0:
            raise CommandError("Count must be positive")

        if count > 10000:
            raise CommandError("Cannot generate more than 10,000 coupons at once")

        # Set validity dates
        now = timezone.now()
        valid_from = now
        valid_until = now + timedelta(days=days_valid)

        # Get campaign if specified
        campaign = None
        if campaign_slug:
            try:
                campaign = PromotionCampaign.objects.get(slug=campaign_slug)
            except PromotionCampaign.DoesNotExist:
                raise CommandError(f"Campaign '{campaign_slug}' not found")

        # Build business rules
        business_rules = {}
        if min_order:
            business_rules['min_order_amount'] = float(min_order)
        if max_discount:
            business_rules['max_discount_amount'] = float(max_discount)

        # Show summary
        self.stdout.write(self.style.SUCCESS(f"\nCoupon Generation Summary:"))
        self.stdout.write(f"  Count: {count}")
        self.stdout.write(f"  Type: {coupon_type}")
        self.stdout.write(f"  Value: {value}")
        self.stdout.write(f"  Prefix: {prefix}")
        self.stdout.write(f"  Valid from: {valid_from.strftime('%Y-%m-%d %H:%M')}")
        self.stdout.write(f"  Valid until: {valid_until.strftime('%Y-%m-%d %H:%M')}")
        self.stdout.write(f"  Status: {status}")
        self.stdout.write(f"  Priority: {priority}")
        self.stdout.write(f"  Stackable: {stackable}")
        if usage_limit:
            self.stdout.write(f"  Usage limit: {usage_limit}")
        self.stdout.write(f"  Usage limit per user: {usage_limit_per_user}")
        if campaign:
            self.stdout.write(f"  Campaign: {campaign.name}")
        if business_rules:
            self.stdout.write(f"  Business rules: {business_rules}")

        if dry_run:
            self.stdout.write(self.style.WARNING("\nDRY RUN - No coupons will be created"))
            return

        # Create coupons
        created_coupons = []
        errors = []

        self.stdout.write(f"\nGenerating {count} coupons...")

        for i in range(count):
            try:
                coupon = CouponCode(
                    name=f"{name_prefix} #{i+1}",
                    description=f"Auto-generated {coupon_type} coupon",
                    coupon_type=coupon_type,
                    status=status,
                    discount_value=value,
                    usage_limit=usage_limit,
                    usage_limit_per_user=usage_limit_per_user,
                    valid_from=valid_from,
                    valid_until=valid_until,
                    priority=priority,
                    is_stackable=stackable,
                    business_rules=business_rules,
                )

                # Generate unique code
                coupon.code = CouponCode.generate_unique_code(prefix=prefix)
                
                # Set max discount amount if specified
                if max_discount and coupon_type == 'percentage':
                    from djmoney.money import Money
                    coupon.max_discount_amount = Money(max_discount, 'USD')

                coupon.full_clean()
                coupon.save()

                # Associate with campaign if specified
                if campaign:
                    campaign.coupons.add(coupon)

                created_coupons.append(coupon)

                # Progress indicator
                if (i + 1) % 100 == 0:
                    self.stdout.write(f"  Created {i + 1} coupons...")

            except Exception as e:
                errors.append(f"Error creating coupon #{i+1}: {str(e)}")
                logger.error(f"Error creating coupon #{i+1}: {str(e)}")

        # Report results
        self.stdout.write(self.style.SUCCESS(f"\nSuccessfully created {len(created_coupons)} coupons"))
        
        if errors:
            self.stdout.write(self.style.ERROR(f"Encountered {len(errors)} errors:"))
            for error in errors[:10]:  # Show first 10 errors
                self.stdout.write(f"  - {error}")
            if len(errors) > 10:
                self.stdout.write(f"  ... and {len(errors) - 10} more errors")

        # Show sample codes
        if created_coupons:
            self.stdout.write(f"\nSample generated codes:")
            for coupon in created_coupons[:5]:
                self.stdout.write(f"  - {coupon.code}")
            if len(created_coupons) > 5:
                self.stdout.write(f"  ... and {len(created_coupons) - 5} more")

        # Update campaign if specified
        if campaign and created_coupons:
            self.stdout.write(f"\nAssociated {len(created_coupons)} coupons with campaign '{campaign.name}'")

        self.stdout.write(self.style.SUCCESS(f"\nCoupon generation completed!")) 