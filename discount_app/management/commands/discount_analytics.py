"""
Management command to analyze discount usage and generate reports.

Usage:
    python manage.py discount_analytics --report summary
    python manage.py discount_analytics --report top-performers --days 30
    python manage.py discount_analytics --report user-behavior --export csv
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.db.models import Count, Sum, Avg, Q, F
from datetime import timedelta, datetime
from decimal import Decimal
import csv
import json

from discount_app.models import (
    DiscountUsage, CouponUsage, CouponCode, VenueDiscount, 
    ServiceDiscount, PlatformDiscount, PromotionCampaign
)


class Command(BaseCommand):
    help = 'Analyze discount usage and generate reports'

    def add_arguments(self, parser):
        parser.add_argument(
            '--report',
            type=str,
            choices=['summary', 'top-performers', 'user-behavior', 'campaign-analysis', 'expiring-coupons'],
            default='summary',
            help='Type of report to generate (default: summary)'
        )
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='Number of days to look back (default: 30)'
        )
        parser.add_argument(
            '--export',
            type=str,
            choices=['csv', 'json'],
            help='Export format (csv or json)'
        )
        parser.add_argument(
            '--output',
            type=str,
            help='Output file path (default: stdout)'
        )
        parser.add_argument(
            '--top-n',
            type=int,
            default=10,
            help='Number of top items to show (default: 10)'
        )

    def handle(self, *args, **options):
        """Handle the command execution."""
        report_type = options['report']
        days = options['days']
        export_format = options['export']
        output_file = options['output']
        top_n = options['top_n']

        # Calculate date range
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)

        self.stdout.write(self.style.SUCCESS(f"Generating {report_type} report for the last {days} days"))
        self.stdout.write(f"Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

        # Generate the requested report
        if report_type == 'summary':
            data = self.generate_summary_report(start_date, end_date)
        elif report_type == 'top-performers':
            data = self.generate_top_performers_report(start_date, end_date, top_n)
        elif report_type == 'user-behavior':
            data = self.generate_user_behavior_report(start_date, end_date)
        elif report_type == 'campaign-analysis':
            data = self.generate_campaign_analysis_report(start_date, end_date)
        elif report_type == 'expiring-coupons':
            data = self.generate_expiring_coupons_report(days)
        else:
            raise CommandError(f"Unknown report type: {report_type}")

        # Output the report
        if export_format and output_file:
            self.export_data(data, export_format, output_file)
        elif export_format:
            self.export_data(data, export_format)
        else:
            self.display_report(data, report_type)

    def generate_summary_report(self, start_date, end_date):
        """Generate a summary report of discount usage."""
        # Discount usage stats
        discount_usage = DiscountUsage.objects.filter(
            used_at__range=[start_date, end_date]
        ).aggregate(
            total_uses=Count('id'),
            total_savings=Sum('discount_amount'),
            avg_discount=Avg('discount_amount'),
            total_original_value=Sum('original_price'),
            total_final_value=Sum('final_price')
        )

        # Coupon usage stats
        coupon_usage = CouponUsage.objects.filter(
            used_at__range=[start_date, end_date]
        ).aggregate(
            total_uses=Count('id'),
            total_savings=Sum('discount_amount'),
            avg_discount=Avg('discount_amount'),
            total_original_value=Sum('original_amount'),
            total_final_value=Sum('final_amount')
        )

        # Active coupons
        active_coupons = CouponCode.objects.filter(
            status='active',
            valid_from__lte=end_date,
            valid_until__gte=start_date
        ).count()

        # Discount types breakdown
        discount_types = DiscountUsage.objects.filter(
            used_at__range=[start_date, end_date]
        ).values('discount_type').annotate(
            count=Count('id'),
            total_savings=Sum('discount_amount')
        ).order_by('-count')

        return {
            'period': f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
            'discount_usage': discount_usage,
            'coupon_usage': coupon_usage,
            'active_coupons': active_coupons,
            'discount_types': list(discount_types),
            'total_uses': (discount_usage['total_uses'] or 0) + (coupon_usage['total_uses'] or 0),
            'total_savings': (discount_usage['total_savings'] or 0) + (coupon_usage['total_savings'] or 0),
        }

    def generate_top_performers_report(self, start_date, end_date, top_n):
        """Generate a report of top-performing discounts."""
        # Top coupons by usage
        top_coupons = CouponCode.objects.filter(
            usage_instances__used_at__range=[start_date, end_date]
        ).annotate(
            uses=Count('usage_instances'),
            total_savings=Sum('usage_instances__discount_amount')
        ).order_by('-uses')[:top_n]

        # Top venue discounts
        top_venue_discounts = VenueDiscount.objects.filter(
            discountusage__used_at__range=[start_date, end_date],
            discountusage__discount_type='VenueDiscount'
        ).annotate(
            uses=Count('discountusage'),
            total_savings=Sum('discountusage__discount_amount')
        ).order_by('-uses')[:top_n]

        # Top platform discounts
        top_platform_discounts = PlatformDiscount.objects.filter(
            discountusage__used_at__range=[start_date, end_date],
            discountusage__discount_type='PlatformDiscount'
        ).annotate(
            uses=Count('discountusage'),
            total_savings=Sum('discountusage__discount_amount')
        ).order_by('-uses')[:top_n]

        return {
            'period': f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
            'top_coupons': [
                {
                    'code': coupon.code,
                    'name': coupon.name,
                    'uses': coupon.uses,
                    'total_savings': float(coupon.total_savings or 0)
                }
                for coupon in top_coupons
            ],
            'top_venue_discounts': [
                {
                    'name': discount.name,
                    'venue': discount.venue.venue_name,
                    'uses': discount.uses,
                    'total_savings': float(discount.total_savings or 0)
                }
                for discount in top_venue_discounts
            ],
            'top_platform_discounts': [
                {
                    'name': discount.name,
                    'uses': discount.uses,
                    'total_savings': float(discount.total_savings or 0)
                }
                for discount in top_platform_discounts
            ]
        }

    def generate_user_behavior_report(self, start_date, end_date):
        """Generate a report on user discount behavior."""
        # User stats
        user_stats = DiscountUsage.objects.filter(
            used_at__range=[start_date, end_date]
        ).values('user').annotate(
            total_uses=Count('id'),
            total_savings=Sum('discount_amount'),
            avg_savings=Avg('discount_amount')
        ).order_by('-total_uses')

        # Power users (top 10% by usage)
        power_user_threshold = max(1, int(len(user_stats) * 0.1))
        power_users = user_stats[:power_user_threshold]

        # Discount type preferences
        type_preferences = DiscountUsage.objects.filter(
            used_at__range=[start_date, end_date]
        ).values('discount_type').annotate(
            unique_users=Count('user', distinct=True),
            total_uses=Count('id')
        ).order_by('-unique_users')

        return {
            'period': f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
            'total_active_users': len(user_stats),
            'power_users': [
                {
                    'user_id': user['user'],
                    'total_uses': user['total_uses'],
                    'total_savings': float(user['total_savings'] or 0),
                    'avg_savings': float(user['avg_savings'] or 0)
                }
                for user in power_users
            ],
            'type_preferences': list(type_preferences)
        }

    def generate_campaign_analysis_report(self, start_date, end_date):
        """Generate a report on campaign performance."""
        campaigns = PromotionCampaign.objects.filter(
            start_date__lte=end_date,
            end_date__gte=start_date
        ).prefetch_related('coupons')

        campaign_data = []
        for campaign in campaigns:
            coupon_usage = CouponUsage.objects.filter(
                coupon__in=campaign.coupons.all(),
                used_at__range=[start_date, end_date]
            ).aggregate(
                total_uses=Count('id'),
                total_savings=Sum('discount_amount'),
                unique_users=Count('user', distinct=True)
            )

            campaign_data.append({
                'name': campaign.name,
                'status': campaign.status,
                'coupons_count': campaign.coupons.count(),
                'total_uses': coupon_usage['total_uses'] or 0,
                'total_savings': float(coupon_usage['total_savings'] or 0),
                'unique_users': coupon_usage['unique_users'] or 0,
                'budget_limit': float(campaign.budget_limit.amount) if campaign.budget_limit else None,
                'current_spend': float(campaign.current_spend.amount) if campaign.current_spend else 0,
            })

        return {
            'period': f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
            'campaigns': campaign_data
        }

    def generate_expiring_coupons_report(self, days):
        """Generate a report of coupons expiring soon."""
        expiry_date = timezone.now() + timedelta(days=days)
        
        expiring_coupons = CouponCode.objects.filter(
            status='active',
            valid_until__lte=expiry_date,
            valid_until__gte=timezone.now()
        ).order_by('valid_until')

        return {
            'expiry_threshold': f"Next {days} days",
            'total_expiring': expiring_coupons.count(),
            'coupons': [
                {
                    'code': coupon.code,
                    'name': coupon.name,
                    'valid_until': coupon.valid_until.strftime('%Y-%m-%d %H:%M'),
                    'usage_count': coupon.current_usage_count,
                    'usage_limit': coupon.usage_limit,
                    'days_remaining': (coupon.valid_until - timezone.now()).days
                }
                for coupon in expiring_coupons
            ]
        }

    def display_report(self, data, report_type):
        """Display the report in a formatted way."""
        if report_type == 'summary':
            self.display_summary_report(data)
        elif report_type == 'top-performers':
            self.display_top_performers_report(data)
        elif report_type == 'user-behavior':
            self.display_user_behavior_report(data)
        elif report_type == 'campaign-analysis':
            self.display_campaign_analysis_report(data)
        elif report_type == 'expiring-coupons':
            self.display_expiring_coupons_report(data)

    def display_summary_report(self, data):
        """Display the summary report."""
        self.stdout.write(f"\n=== DISCOUNT SUMMARY REPORT ===")
        self.stdout.write(f"Period: {data['period']}")
        self.stdout.write(f"")
        
        self.stdout.write(f"Overall Statistics:")
        self.stdout.write(f"  Total Uses: {data['total_uses']:,}")
        self.stdout.write(f"  Total Savings: ${data['total_savings']:,.2f}")
        self.stdout.write(f"  Active Coupons: {data['active_coupons']:,}")
        self.stdout.write(f"")
        
        self.stdout.write(f"Regular Discounts:")
        du = data['discount_usage']
        self.stdout.write(f"  Uses: {du['total_uses'] or 0:,}")
        self.stdout.write(f"  Savings: ${du['total_savings'] or 0:,.2f}")
        self.stdout.write(f"  Avg Discount: ${du['avg_discount'] or 0:.2f}")
        self.stdout.write(f"")
        
        self.stdout.write(f"Coupon Codes:")
        cu = data['coupon_usage']
        self.stdout.write(f"  Uses: {cu['total_uses'] or 0:,}")
        self.stdout.write(f"  Savings: ${cu['total_savings'] or 0:,.2f}")
        self.stdout.write(f"  Avg Discount: ${cu['avg_discount'] or 0:.2f}")
        self.stdout.write(f"")
        
        if data['discount_types']:
            self.stdout.write(f"Discount Types Breakdown:")
            for dt in data['discount_types']:
                self.stdout.write(f"  {dt['discount_type']}: {dt['count']:,} uses, ${dt['total_savings'] or 0:,.2f} savings")

    def display_top_performers_report(self, data):
        """Display the top performers report."""
        self.stdout.write(f"\n=== TOP PERFORMERS REPORT ===")
        self.stdout.write(f"Period: {data['period']}")
        self.stdout.write(f"")
        
        self.stdout.write(f"Top Coupon Codes:")
        for i, coupon in enumerate(data['top_coupons'], 1):
            self.stdout.write(f"  {i}. {coupon['code']} - {coupon['name']}")
            self.stdout.write(f"     Uses: {coupon['uses']:,}, Savings: ${coupon['total_savings']:,.2f}")
        self.stdout.write(f"")
        
        self.stdout.write(f"Top Venue Discounts:")
        for i, discount in enumerate(data['top_venue_discounts'], 1):
            self.stdout.write(f"  {i}. {discount['name']} ({discount['venue']})")
            self.stdout.write(f"     Uses: {discount['uses']:,}, Savings: ${discount['total_savings']:,.2f}")
        self.stdout.write(f"")
        
        self.stdout.write(f"Top Platform Discounts:")
        for i, discount in enumerate(data['top_platform_discounts'], 1):
            self.stdout.write(f"  {i}. {discount['name']}")
            self.stdout.write(f"     Uses: {discount['uses']:,}, Savings: ${discount['total_savings']:,.2f}")

    def display_user_behavior_report(self, data):
        """Display the user behavior report."""
        self.stdout.write(f"\n=== USER BEHAVIOR REPORT ===")
        self.stdout.write(f"Period: {data['period']}")
        self.stdout.write(f"")
        
        self.stdout.write(f"Total Active Users: {data['total_active_users']:,}")
        self.stdout.write(f"")
        
        self.stdout.write(f"Power Users (Top 10%):")
        for i, user in enumerate(data['power_users'], 1):
            self.stdout.write(f"  {i}. User ID: {user['user_id']}")
            self.stdout.write(f"     Uses: {user['total_uses']:,}, Total Savings: ${user['total_savings']:,.2f}")
            self.stdout.write(f"     Avg Savings: ${user['avg_savings']:,.2f}")
        self.stdout.write(f"")
        
        self.stdout.write(f"Discount Type Preferences:")
        for pref in data['type_preferences']:
            self.stdout.write(f"  {pref['discount_type']}: {pref['unique_users']:,} users, {pref['total_uses']:,} uses")

    def display_campaign_analysis_report(self, data):
        """Display the campaign analysis report."""
        self.stdout.write(f"\n=== CAMPAIGN ANALYSIS REPORT ===")
        self.stdout.write(f"Period: {data['period']}")
        self.stdout.write(f"")
        
        for campaign in data['campaigns']:
            self.stdout.write(f"Campaign: {campaign['name']} ({campaign['status']})")
            self.stdout.write(f"  Coupons: {campaign['coupons_count']:,}")
            self.stdout.write(f"  Uses: {campaign['total_uses']:,}")
            self.stdout.write(f"  Savings: ${campaign['total_savings']:,.2f}")
            self.stdout.write(f"  Unique Users: {campaign['unique_users']:,}")
            if campaign['budget_limit']:
                budget_used = (campaign['current_spend'] / campaign['budget_limit']) * 100
                self.stdout.write(f"  Budget: ${campaign['current_spend']:,.2f} / ${campaign['budget_limit']:,.2f} ({budget_used:.1f}%)")
            self.stdout.write(f"")

    def display_expiring_coupons_report(self, data):
        """Display the expiring coupons report."""
        self.stdout.write(f"\n=== EXPIRING COUPONS REPORT ===")
        self.stdout.write(f"Expiry Threshold: {data['expiry_threshold']}")
        self.stdout.write(f"Total Expiring: {data['total_expiring']:,}")
        self.stdout.write(f"")
        
        for coupon in data['coupons']:
            self.stdout.write(f"Code: {coupon['code']} - {coupon['name']}")
            self.stdout.write(f"  Expires: {coupon['valid_until']} ({coupon['days_remaining']} days)")
            self.stdout.write(f"  Usage: {coupon['usage_count']}/{coupon['usage_limit'] or '∞'}")
            self.stdout.write(f"")

    def export_data(self, data, format_type, filename=None):
        """Export data to file or stdout."""
        if format_type == 'json':
            json_data = json.dumps(data, indent=2, default=str)
            if filename:
                with open(filename, 'w') as f:
                    f.write(json_data)
                self.stdout.write(f"Data exported to {filename}")
            else:
                self.stdout.write(json_data)
        
        elif format_type == 'csv':
            # CSV export depends on report type
            if filename:
                with open(filename, 'w', newline='') as f:
                    self.write_csv_data(data, f)
                self.stdout.write(f"Data exported to {filename}")
            else:
                import sys
                self.write_csv_data(data, sys.stdout)

    def write_csv_data(self, data, file):
        """Write data as CSV format."""
        writer = csv.writer(file)
        
        # Write based on available data structure
        if 'coupons' in data:  # Expiring coupons
            writer.writerow(['Code', 'Name', 'Valid Until', 'Usage Count', 'Usage Limit', 'Days Remaining'])
            for coupon in data['coupons']:
                writer.writerow([
                    coupon['code'], coupon['name'], coupon['valid_until'],
                    coupon['usage_count'], coupon['usage_limit'] or '',
                    coupon['days_remaining']
                ])
        elif 'campaigns' in data:  # Campaign analysis
            writer.writerow(['Name', 'Status', 'Coupons', 'Uses', 'Savings', 'Unique Users', 'Budget Limit', 'Current Spend'])
            for campaign in data['campaigns']:
                writer.writerow([
                    campaign['name'], campaign['status'], campaign['coupons_count'],
                    campaign['total_uses'], campaign['total_savings'],
                    campaign['unique_users'], campaign['budget_limit'] or '',
                    campaign['current_spend']
                ])
        else:
            # Generic format for other reports
            writer.writerow(['Report Type', 'Period', 'Data'])
            writer.writerow(['Summary', data.get('period', ''), str(data)]) 