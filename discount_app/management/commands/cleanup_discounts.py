"""
Management command to clean up expired discounts and maintain the discount system.

Usage:
    python manage.py cleanup_discounts --expired
    python manage.py cleanup_discounts --unused --days 90
    python manage.py cleanup_discounts --update-status
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.db.models import Count, Q, F
from datetime import timedelta
import logging

from discount_app.models import (
    CouponCode, VenueDiscount, ServiceDiscount, PlatformDiscount,
    DiscountUsage, CouponUsage, PromotionCampaign
)


logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Clean up expired discounts and maintain the discount system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--expired',
            action='store_true',
            help='Clean up expired discounts and coupons'
        )
        parser.add_argument(
            '--unused',
            action='store_true',
            help='Clean up unused discounts/coupons older than specified days'
        )
        parser.add_argument(
            '--update-status',
            action='store_true',
            help='Update status of discounts based on current date/usage'
        )
        parser.add_argument(
            '--days',
            type=int,
            default=90,
            help='Days threshold for unused item cleanup (default: 90)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be cleaned up without actually doing it'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force cleanup without confirmation'
        )

    def handle(self, *args, **options):
        """Handle the command execution."""
        expired = options['expired']
        unused = options['unused']
        update_status = options['update_status']
        days = options['days']
        dry_run = options['dry_run']
        force = options['force']

        if not any([expired, unused, update_status]):
            # Default to updating status if no specific action specified
            update_status = True

        self.stdout.write(self.style.SUCCESS("Starting discount system cleanup..."))

        if update_status:
            self.update_discount_status(dry_run)

        if expired:
            self.cleanup_expired_items(dry_run, force)

        if unused:
            self.cleanup_unused_items(days, dry_run, force)

        self.stdout.write(self.style.SUCCESS("Cleanup completed!"))

    def update_discount_status(self, dry_run=False):
        """Update status of discounts based on current date and usage."""
        self.stdout.write("\n=== Updating Discount Status ===")
        now = timezone.now()
        
        # Update expired coupons
        expired_coupons = CouponCode.objects.filter(
            Q(valid_until__lt=now) | Q(status='active'),
            ~Q(status='expired')
        )
        
        # Separate truly expired from depleted
        truly_expired = expired_coupons.filter(valid_until__lt=now)
        depleted = expired_coupons.filter(
            current_usage_count__gte=F('usage_limit'),
            usage_limit__isnull=False
        ).exclude(valid_until__lt=now)

        if dry_run:
            self.stdout.write(f"[DRY RUN] Would mark {truly_expired.count()} coupons as expired")
            self.stdout.write(f"[DRY RUN] Would mark {depleted.count()} coupons as depleted")
        else:
            expired_count = truly_expired.update(status='expired')
            depleted_count = depleted.update(status='depleted')
            self.stdout.write(f"Updated {expired_count} coupons to expired status")
            self.stdout.write(f"Updated {depleted_count} coupons to depleted status")
            logger.info(f"Updated {expired_count} expired and {depleted_count} depleted coupons")

        # Update expired campaigns
        expired_campaigns = PromotionCampaign.objects.filter(
            end_date__lt=now,
            ~Q(status='expired')
        )

        if dry_run:
            self.stdout.write(f"[DRY RUN] Would mark {expired_campaigns.count()} campaigns as expired")
        else:
            campaign_count = expired_campaigns.update(status='expired')
            self.stdout.write(f"Updated {campaign_count} campaigns to expired status")
            logger.info(f"Updated {campaign_count} expired campaigns")

        # Update scheduled items to active
        scheduled_coupons = CouponCode.objects.filter(
            status='draft',
            valid_from__lte=now,
            valid_until__gt=now
        )

        scheduled_campaigns = PromotionCampaign.objects.filter(
            status='draft',
            start_date__lte=now,
            end_date__gt=now
        )

        if dry_run:
            self.stdout.write(f"[DRY RUN] Would activate {scheduled_coupons.count()} scheduled coupons")
            self.stdout.write(f"[DRY RUN] Would activate {scheduled_campaigns.count()} scheduled campaigns")
        else:
            activated_coupons = scheduled_coupons.update(status='active')
            activated_campaigns = scheduled_campaigns.update(status='active')
            self.stdout.write(f"Activated {activated_coupons} scheduled coupons")
            self.stdout.write(f"Activated {activated_campaigns} scheduled campaigns")
            logger.info(f"Activated {activated_coupons} coupons and {activated_campaigns} campaigns")

    def cleanup_expired_items(self, dry_run=False, force=False):
        """Clean up expired discounts and coupons."""
        self.stdout.write("\n=== Cleaning Up Expired Items ===")
        now = timezone.now()
        
        # Find expired items
        expired_coupons = CouponCode.objects.filter(
            Q(valid_until__lt=now) | Q(status='expired')
        )
        
        expired_campaigns = PromotionCampaign.objects.filter(
            Q(end_date__lt=now) | Q(status='expired')
        )
        
        expired_venue_discounts = VenueDiscount.objects.filter(
            end_date__lt=now
        )
        
        expired_service_discounts = ServiceDiscount.objects.filter(
            end_date__lt=now
        )
        
        expired_platform_discounts = PlatformDiscount.objects.filter(
            end_date__lt=now
        )

        total_expired = (
            expired_coupons.count() +
            expired_campaigns.count() +
            expired_venue_discounts.count() +
            expired_service_discounts.count() +
            expired_platform_discounts.count()
        )

        if total_expired == 0:
            self.stdout.write("No expired items found.")
            return

        self.stdout.write(f"Found {total_expired} expired items:")
        self.stdout.write(f"  - Coupons: {expired_coupons.count()}")
        self.stdout.write(f"  - Campaigns: {expired_campaigns.count()}")
        self.stdout.write(f"  - Venue Discounts: {expired_venue_discounts.count()}")
        self.stdout.write(f"  - Service Discounts: {expired_service_discounts.count()}")
        self.stdout.write(f"  - Platform Discounts: {expired_platform_discounts.count()}")

        if dry_run:
            self.stdout.write("[DRY RUN] Would delete these expired items")
            return

        if not force:
            confirm = input(f"\nAre you sure you want to delete {total_expired} expired items? [y/N]: ")
            if confirm.lower() != 'y':
                self.stdout.write("Cleanup cancelled.")
                return

        # Delete expired items
        deleted_counts = {}
        
        deleted_counts['coupons'] = expired_coupons.delete()[0]
        deleted_counts['campaigns'] = expired_campaigns.delete()[0]
        deleted_counts['venue_discounts'] = expired_venue_discounts.delete()[0]
        deleted_counts['service_discounts'] = expired_service_discounts.delete()[0]
        deleted_counts['platform_discounts'] = expired_platform_discounts.delete()[0]

        total_deleted = sum(deleted_counts.values())
        self.stdout.write(f"Deleted {total_deleted} expired items:")
        for item_type, count in deleted_counts.items():
            if count > 0:
                self.stdout.write(f"  - {item_type.replace('_', ' ').title()}: {count}")

        logger.info(f"Deleted {total_deleted} expired discount items")

    def cleanup_unused_items(self, days, dry_run=False, force=False):
        """Clean up unused discounts/coupons older than specified days."""
        self.stdout.write(f"\n=== Cleaning Up Unused Items (>{days} days old) ===")
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # Find unused coupons
        unused_coupons = CouponCode.objects.filter(
            created_at__lt=cutoff_date,
            current_usage_count=0
        ).exclude(status='active')  # Keep active unused coupons
        
        # Find unused venue discounts
        used_venue_discount_ids = DiscountUsage.objects.filter(
            discount_type='VenueDiscount'
        ).values_list('discount_id', flat=True)
        
        unused_venue_discounts = VenueDiscount.objects.filter(
            created_at__lt=cutoff_date,
            current_usage_count=0
        ).exclude(id__in=used_venue_discount_ids)
        
        # Find unused service discounts
        used_service_discount_ids = DiscountUsage.objects.filter(
            discount_type='ServiceDiscount'
        ).values_list('discount_id', flat=True)
        
        unused_service_discounts = ServiceDiscount.objects.filter(
            created_at__lt=cutoff_date,
            current_usage_count=0
        ).exclude(id__in=used_service_discount_ids)
        
        # Find unused platform discounts
        used_platform_discount_ids = DiscountUsage.objects.filter(
            discount_type='PlatformDiscount'
        ).values_list('discount_id', flat=True)
        
        unused_platform_discounts = PlatformDiscount.objects.filter(
            created_at__lt=cutoff_date,
            current_usage_count=0
        ).exclude(id__in=used_platform_discount_ids)

        total_unused = (
            unused_coupons.count() +
            unused_venue_discounts.count() +
            unused_service_discounts.count() +
            unused_platform_discounts.count()
        )

        if total_unused == 0:
            self.stdout.write(f"No unused items older than {days} days found.")
            return

        self.stdout.write(f"Found {total_unused} unused items older than {days} days:")
        self.stdout.write(f"  - Coupons: {unused_coupons.count()}")
        self.stdout.write(f"  - Venue Discounts: {unused_venue_discounts.count()}")
        self.stdout.write(f"  - Service Discounts: {unused_service_discounts.count()}")
        self.stdout.write(f"  - Platform Discounts: {unused_platform_discounts.count()}")

        if dry_run:
            self.stdout.write(f"[DRY RUN] Would delete these {total_unused} unused items")
            return

        if not force:
            confirm = input(f"\nAre you sure you want to delete {total_unused} unused items? [y/N]: ")
            if confirm.lower() != 'y':
                self.stdout.write("Cleanup cancelled.")
                return

        # Delete unused items
        deleted_counts = {}
        
        deleted_counts['coupons'] = unused_coupons.delete()[0]
        deleted_counts['venue_discounts'] = unused_venue_discounts.delete()[0]
        deleted_counts['service_discounts'] = unused_service_discounts.delete()[0]
        deleted_counts['platform_discounts'] = unused_platform_discounts.delete()[0]

        total_deleted = sum(deleted_counts.values())
        self.stdout.write(f"Deleted {total_deleted} unused items:")
        for item_type, count in deleted_counts.items():
            if count > 0:
                self.stdout.write(f"  - {item_type.replace('_', ' ').title()}: {count}")

        logger.info(f"Deleted {total_deleted} unused discount items older than {days} days")

    def get_summary_stats(self):
        """Get summary statistics about the discount system."""
        now = timezone.now()
        
        stats = {
            'active_coupons': CouponCode.objects.filter(
                status='active',
                valid_from__lte=now,
                valid_until__gt=now
            ).count(),
            'expired_coupons': CouponCode.objects.filter(
                Q(valid_until__lt=now) | Q(status='expired')
            ).count(),
            'depleted_coupons': CouponCode.objects.filter(
                status='depleted'
            ).count(),
            'active_campaigns': PromotionCampaign.objects.filter(
                status='active',
                start_date__lte=now,
                end_date__gt=now
            ).count(),
            'total_usage_last_30_days': DiscountUsage.objects.filter(
                used_at__gte=now - timedelta(days=30)
            ).count() + CouponUsage.objects.filter(
                used_at__gte=now - timedelta(days=30)
            ).count(),
        }
        
        return stats 