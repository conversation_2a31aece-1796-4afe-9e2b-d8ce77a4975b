"""
Interactive Discount Calculator Component

A real-time calculator that shows:
- Price calculations
- Savings amount
- Discount effects
- Multiple discount combinations
"""

from django_components import component
from decimal import Decimal
from ..models import DiscountType


@component.register("discount_calculator")
class DiscountCalculator(component.Component):
    """
    Interactive discount calculator with real-time updates.
    
    Props:
    - original_price: Base price for calculations
    - discounts: List of applicable discounts
    - show_breakdown: Boolean to show detailed breakdown
    - currency_symbol: Currency symbol to display
    - calculator_id: Unique ID for multiple calculators on same page
    """
    
    template_name = "discount_app/components/discount_calculator.html"
    
    def get_context_data(self, **kwargs):
        """Prepare context data for the calculator."""
        original_price = Decimal(str(kwargs.get('original_price', 0)))
        discounts = kwargs.get('discounts', [])
        
        # Calculate discount effects
        calculations = self._calculate_discounts(original_price, discounts)
        
        # Format display values
        display_data = self._format_display_values(calculations, kwargs.get('currency_symbol', '$'))
        
        return {
            'original_price': original_price,
            'discounts': discounts,
            'calculations': calculations,
            'display_data': display_data,
            'show_breakdown': kwargs.get('show_breakdown', True),
            'calculator_id': kwargs.get('calculator_id', 'discount-calc'),
            'currency_symbol': kwargs.get('currency_symbol', '$'),
        }
    
    def _calculate_discounts(self, original_price, discounts):
        """Calculate the effect of multiple discounts."""
        if not discounts:
            return {
                'total_savings': Decimal('0'),
                'final_price': original_price,
                'discount_percentage': Decimal('0'),
                'breakdown': []
            }
        
        current_price = original_price
        total_savings = Decimal('0')
        breakdown = []
        
        for discount in discounts:
            if hasattr(discount, 'discount_type') and hasattr(discount, 'discount_value'):
                discount_amount = self._calculate_single_discount(current_price, discount)
                
                breakdown.append({
                    'name': discount.name,
                    'type': discount.discount_type,
                    'value': discount.discount_value,
                    'amount': discount_amount,
                    'price_before': current_price,
                    'price_after': current_price - discount_amount
                })
                
                current_price -= discount_amount
                total_savings += discount_amount
        
        # Calculate overall discount percentage
        discount_percentage = (total_savings / original_price * 100) if original_price > 0 else Decimal('0')
        
        return {
            'total_savings': total_savings,
            'final_price': current_price,
            'discount_percentage': discount_percentage,
            'breakdown': breakdown
        }
    
    def _calculate_single_discount(self, price, discount):
        """Calculate discount amount for a single discount."""
        if discount.discount_type == DiscountType.PERCENTAGE:
            # Ensure percentage is between 0 and 80
            percentage = min(max(discount.discount_value, 0), 80)
            return (price * percentage) / 100
        else:  # Fixed amount
            # Ensure discount doesn't exceed the price
            return min(discount.discount_value, price)
    
    def _format_display_values(self, calculations, currency_symbol):
        """Format values for display."""
        return {
            'total_savings_formatted': f"{currency_symbol}{calculations['total_savings']:.2f}",
            'final_price_formatted': f"{currency_symbol}{calculations['final_price']:.2f}",
            'discount_percentage_formatted': f"{calculations['discount_percentage']:.1f}%",
            'breakdown_formatted': [
                {
                    **item,
                    'amount_formatted': f"{currency_symbol}{item['amount']:.2f}",
                    'price_before_formatted': f"{currency_symbol}{item['price_before']:.2f}",
                    'price_after_formatted': f"{currency_symbol}{item['price_after']:.2f}",
                }
                for item in calculations['breakdown']
            ]
        }
    
    class Media:
        css = {
            'all': ('css/discount_app/discount_calculator.css',)
        }
        js = ('js/discount_app/discount_calculator.js',)
