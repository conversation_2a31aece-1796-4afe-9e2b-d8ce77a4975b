"""
Modern Discount Card Component

A reusable component for displaying discount information with:
- Responsive Bootstrap 5 design
- Hover animations and transitions
- Progress bars for time-limited offers
- Countdown timers
- Interactive elements
"""

from django_components import component
from django.utils import timezone
from datetime import timedelta


@component.register("discount_card")
class DiscountCard(component.Component):
    """
    Modern discount card component with animations and interactive features.
    
    Props:
    - discount: Discount object (VenueDiscount, ServiceDiscount, or PlatformDiscount)
    - show_countdown: Boolean to show/hide countdown timer
    - show_progress: Boolean to show/hide progress bar
    - card_size: 'sm', 'md', 'lg' for different card sizes
    - hover_effect: Boolean to enable/disable hover animations
    """
    
    template_name = "discount_app/components/discount_card.html"
    
    def get_context_data(self, discount, **kwargs):
        """Prepare context data for the discount card."""
        now = timezone.now()
        
        # Calculate time remaining
        time_remaining = None
        progress_percentage = 0
        
        if discount.end_date:
            time_remaining = discount.end_date - now
            total_duration = discount.end_date - discount.start_date
            elapsed_duration = now - discount.start_date
            
            if total_duration.total_seconds() > 0:
                progress_percentage = min(
                    100, 
                    (elapsed_duration.total_seconds() / total_duration.total_seconds()) * 100
                )
        
        # Determine discount status
        status = discount.get_status()
        status_class = {
            'ACTIVE': 'success',
            'SCHEDULED': 'warning', 
            'EXPIRED': 'danger',
            'PENDING': 'secondary'
        }.get(status, 'secondary')
        
        # Calculate savings display
        if hasattr(discount, 'service') and discount.service:
            original_price = discount.service.price_min
            savings = discount.calculate_discount_amount(original_price)
            final_price = original_price - savings
        elif hasattr(discount, 'venue') and discount.venue:
            # For venue discounts, show percentage or fixed amount
            original_price = None
            savings = discount.discount_value
            final_price = None
        else:
            original_price = None
            savings = discount.discount_value
            final_price = None
        
        return {
            'discount': discount,
            'time_remaining': time_remaining,
            'progress_percentage': progress_percentage,
            'status': status,
            'status_class': status_class,
            'original_price': original_price,
            'savings': savings,
            'final_price': final_price,
            'show_countdown': kwargs.get('show_countdown', True),
            'show_progress': kwargs.get('show_progress', True),
            'card_size': kwargs.get('card_size', 'md'),
            'hover_effect': kwargs.get('hover_effect', True),
        }
    
    class Media:
        css = {
            'all': ('css/discount_app/discount_card.css',)
        }
        js = ('js/discount_app/discount_card.js',)
