"""
Discount Badge Component

Displays status and type badges for discounts with:
- Dynamic styling based on status
- Accessibility features
- Consistent design system
"""

from django_components import component
from django.utils import timezone


@component.register("discount_badge")
class DiscountBadge(component.Component):
    """
    Discount badge component for status and type indicators.
    
    Props:
    - discount: Discount object
    - badge_type: 'status', 'type', 'value', or 'custom'
    - size: 'sm', 'md', 'lg'
    - variant: 'pill', 'rounded', 'square'
    - custom_text: Text for custom badges
    - custom_class: Additional CSS classes
    """
    
    template_name = "discount_app/components/discount_badge.html"
    
    def get_context_data(self, discount, **kwargs):
        """Prepare context data for the badge."""
        badge_type = kwargs.get('badge_type', 'status')
        
        badge_data = self._get_badge_data(discount, badge_type, kwargs)
        
        return {
            'discount': discount,
            'badge_type': badge_type,
            'badge_data': badge_data,
            'size': kwargs.get('size', 'md'),
            'variant': kwargs.get('variant', 'rounded'),
            'custom_text': kwargs.get('custom_text', ''),
            'custom_class': kwargs.get('custom_class', ''),
        }
    
    def _get_badge_data(self, discount, badge_type, kwargs):
        """Generate badge data based on type."""
        if badge_type == 'status':
            return self._get_status_badge_data(discount)
        elif badge_type == 'type':
            return self._get_type_badge_data(discount)
        elif badge_type == 'value':
            return self._get_value_badge_data(discount)
        elif badge_type == 'custom':
            return {
                'text': kwargs.get('custom_text', ''),
                'class': kwargs.get('custom_class', 'bg-secondary'),
                'icon': kwargs.get('custom_icon', '')
            }
        else:
            return {'text': '', 'class': 'bg-secondary', 'icon': ''}
    
    def _get_status_badge_data(self, discount):
        """Get badge data for discount status."""
        status = discount.get_status()
        
        status_config = {
            'ACTIVE': {
                'text': 'Active',
                'class': 'bg-success',
                'icon': 'fas fa-check-circle'
            },
            'SCHEDULED': {
                'text': 'Scheduled',
                'class': 'bg-warning',
                'icon': 'fas fa-clock'
            },
            'EXPIRED': {
                'text': 'Expired',
                'class': 'bg-danger',
                'icon': 'fas fa-times-circle'
            },
            'PENDING': {
                'text': 'Pending',
                'class': 'bg-secondary',
                'icon': 'fas fa-hourglass-half'
            }
        }
        
        return status_config.get(status, status_config['PENDING'])
    
    def _get_type_badge_data(self, discount):
        """Get badge data for discount type."""
        if hasattr(discount, 'service') and discount.service:
            return {
                'text': 'Service',
                'class': 'bg-primary',
                'icon': 'fas fa-concierge-bell'
            }
        elif hasattr(discount, 'venue') and discount.venue:
            return {
                'text': 'Venue',
                'class': 'bg-info',
                'icon': 'fas fa-building'
            }
        else:
            return {
                'text': 'Platform',
                'class': 'bg-warning',
                'icon': 'fas fa-globe'
            }
    
    def _get_value_badge_data(self, discount):
        """Get badge data for discount value."""
        if discount.discount_type == 'PERCENTAGE':
            return {
                'text': f"{discount.discount_value:.0f}% OFF",
                'class': 'bg-success',
                'icon': 'fas fa-percentage'
            }
        else:
            return {
                'text': f"${discount.discount_value:.2f} OFF",
                'class': 'bg-success',
                'icon': 'fas fa-dollar-sign'
            }
    
    class Media:
        css = {
            'all': ('css/discount_app/discount_badge.css',)
        }
