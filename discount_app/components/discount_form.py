"""
Modern Discount Form Component

A reusable component for discount creation and editing with:
- Real-time validation
- Dynamic field updates
- Bootstrap 5 styling
- HTMX integration for seamless UX
"""

from django_components import component
from django import forms
from django.urls import reverse
from ..models import DiscountType


@component.register("discount_form")
class DiscountForm(component.Component):
    """
    Modern discount form component with real-time validation and dynamic updates.
    
    Props:
    - form: Django form instance
    - form_type: 'create' or 'edit'
    - discount_type: 'service', 'venue', or 'platform'
    - submit_url: URL for form submission
    - cancel_url: URL for cancel action
    - show_preview: Boolean to show/hide live preview
    """
    
    template_name = "discount_app/components/discount_form.html"
    
    def get_context_data(self, form, **kwargs):
        """Prepare context data for the discount form."""
        form_type = kwargs.get('form_type', 'create')
        discount_type = kwargs.get('discount_type', 'service')
        
        # Determine form configuration
        form_config = {
            'title': self._get_form_title(form_type, discount_type),
            'submit_text': 'Update Discount' if form_type == 'edit' else 'Create Discount',
            'submit_class': 'btn-primary',
            'show_cancel': kwargs.get('show_cancel', True),
            'show_preview': kwargs.get('show_preview', True),
        }
        
        # HTMX configuration
        htmx_config = {
            'submit_url': kwargs.get('submit_url', ''),
            'preview_url': kwargs.get('preview_url', ''),
            'validation_url': kwargs.get('validation_url', ''),
        }
        
        # Field groups for better organization
        field_groups = self._organize_form_fields(form)
        
        return {
            'form': form,
            'form_type': form_type,
            'discount_type': discount_type,
            'form_config': form_config,
            'htmx_config': htmx_config,
            'field_groups': field_groups,
            'cancel_url': kwargs.get('cancel_url', ''),
        }
    
    def _get_form_title(self, form_type, discount_type):
        """Generate appropriate form title."""
        action = 'Edit' if form_type == 'edit' else 'Create'
        type_name = discount_type.title()
        return f"{action} {type_name} Discount"
    
    def _organize_form_fields(self, form):
        """Organize form fields into logical groups."""
        field_groups = {
            'basic': {
                'title': 'Basic Information',
                'icon': 'fas fa-info-circle',
                'fields': ['name', 'description']
            },
            'discount': {
                'title': 'Discount Details',
                'icon': 'fas fa-percentage',
                'fields': ['discount_type', 'discount_value']
            },
            'timing': {
                'title': 'Schedule',
                'icon': 'fas fa-calendar-alt',
                'fields': ['start_date', 'end_date']
            },
            'limits': {
                'title': 'Usage Limits',
                'icon': 'fas fa-users',
                'fields': ['usage_limit', 'per_user_limit']
            }
        }
        
        # Filter out fields that don't exist in the form
        for group in field_groups.values():
            group['fields'] = [
                field for field in group['fields'] 
                if field in form.fields
            ]
        
        # Remove empty groups
        field_groups = {
            key: group for key, group in field_groups.items()
            if group['fields']
        }
        
        return field_groups
    
    class Media:
        css = {
            'all': ('css/discount_app/discount_form.css',)
        }
        js = ('js/discount_app/discount_form.js',)
