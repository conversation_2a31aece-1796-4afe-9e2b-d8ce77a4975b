"""
Django Components for Discount App

This module provides reusable UI components for the discount application,
following modern component-based architecture patterns.

Components included:
- DiscountCard: Modern discount display cards with animations
- DiscountForm: Enhanced form components with real-time validation
- DiscountCalculator: Interactive discount calculation widget
- DiscountBadge: Status and type badges for discounts
- DiscountSkeleton: Loading skeleton components
"""

from .discount_card import DiscountCard
from .discount_form import DiscountForm
from .discount_calculator import DiscountCalculator
from .discount_badge import DiscountBadge
from .discount_skeleton import DiscountSkeleton

__all__ = [
    'DiscountCard',
    'DiscountForm', 
    'DiscountCalculator',
    'DiscountBadge',
    'DiscountSkeleton',
]
