"""Tests for business rules engine and discount engine functionality."""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta

from discount_app.models import CouponCode, CouponType, CouponStatus
from discount_app.business_rules import BusinessRulesEngine, DiscountContext, DiscountResult
from discount_app.discount_engine import AdvancedDiscountEngine, DiscountItem, DiscountCalculation


User = get_user_model()


class BusinessRulesEngineTest(TestCase):
    """Test cases for the business rules engine."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.engine = BusinessRulesEngine()
        
        self.context = DiscountContext(
            user=self.user,
            cart_total=Decimal('100.00'),
            items=[
                {'id': 1, 'category_id': 1, 'price': Decimal('50.00'), 'quantity': 1},
                {'id': 2, 'category_id': 2, 'price': Decimal('50.00'), 'quantity': 1}
            ],
            categories=[1, 2],
            venues=[1],
            services=[1, 2]
        )

    def test_user_eligibility_validation(self):
        """Test user eligibility validation."""
        # Test new user only rule
        rules = {'new_users_only': True}
        
        # Simulate new user
        result = self.engine.validate_user_eligibility(self.context, rules)
        self.assertTrue(result.is_valid)
        
        # Test user type restrictions
        rules = {'user_type_restrictions': ['premium']}
        result = self.engine.validate_user_eligibility(self.context, rules)
        # Should be invalid since user doesn't have premium type
        self.assertFalse(result.is_valid)

    def test_booking_eligibility_validation(self):
        """Test booking eligibility validation."""
        # Test minimum booking value
        rules = {'min_booking_value': Decimal('50.00')}
        result = self.engine.validate_booking_eligibility(self.context, rules)
        self.assertTrue(result.is_valid)
        
        # Test maximum booking value
        rules = {'max_booking_value': Decimal('150.00')}
        result = self.engine.validate_booking_eligibility(self.context, rules)
        self.assertTrue(result.is_valid)
        
        # Test minimum booking value too high
        rules = {'min_booking_value': Decimal('200.00')}
        result = self.engine.validate_booking_eligibility(self.context, rules)
        self.assertFalse(result.is_valid)

    def test_category_restrictions(self):
        """Test category-based restrictions."""
        # Test allowed categories
        rules = {'allowed_categories': [1, 2]}
        result = self.engine.validate_booking_eligibility(self.context, rules)
        self.assertTrue(result.is_valid)
        
        # Test excluded categories
        rules = {'excluded_categories': [3, 4]}
        result = self.engine.validate_booking_eligibility(self.context, rules)
        self.assertTrue(result.is_valid)
        
        # Test restricted categories
        rules = {'allowed_categories': [3, 4]}
        result = self.engine.validate_booking_eligibility(self.context, rules)
        self.assertFalse(result.is_valid)

    def test_time_restrictions(self):
        """Test time-based restrictions."""
        now = timezone.now()
        
        # Test day of week restriction
        rules = {
            'time_restrictions': {
                'allowed_days': [now.weekday()]  # Today should be allowed
            }
        }
        result = self.engine.validate_time_restrictions(self.context, rules)
        self.assertTrue(result.is_valid)
        
        # Test hour restriction
        rules = {
            'time_restrictions': {
                'allowed_hours': [now.hour]  # Current hour should be allowed
            }
        }
        result = self.engine.validate_time_restrictions(self.context, rules)
        self.assertTrue(result.is_valid)

    def test_usage_limit_validation(self):
        """Test usage limit validation."""
        # Create a coupon with usage limits
        coupon = CouponCode.objects.create(
            name='Limited Coupon',
            coupon_type=CouponType.PERCENTAGE,
            discount_value=Decimal('10.00'),
            usage_limit=100,
            usage_limit_per_user=5,
            current_usage_count=50,
            valid_from=timezone.now(),
            valid_until=timezone.now() + timedelta(days=30)
        )
        
        rules = {
            'max_uses_per_user': 5,
            'max_total_uses': 100
        }
        
        # Should be valid (within limits)
        result = self.engine.validate_usage_limits(self.context, rules, coupon)
        self.assertTrue(result.is_valid)
        
        # Test exceeded total usage
        coupon.current_usage_count = 100
        coupon.save()
        result = self.engine.validate_usage_limits(self.context, rules, coupon)
        self.assertFalse(result.is_valid)

    def test_combination_rules(self):
        """Test discount combination rules."""
        # Test stackable discount
        rules = {'can_combine_with_coupons': True}
        result = self.engine.validate_combination_rules(self.context, rules)
        self.assertTrue(result.is_valid)
        
        # Test non-stackable discount
        rules = {'can_combine_with_coupons': False}
        result = self.engine.validate_combination_rules(self.context, rules)
        self.assertTrue(result.is_valid)  # Always valid unless other coupons present

    def test_comprehensive_validation(self):
        """Test comprehensive business rules validation."""
        # Create a complex rule set
        rules = {
            'min_booking_value': Decimal('50.00'),
            'max_booking_value': Decimal('500.00'),
            'allowed_categories': [1, 2],
            'new_users_only': False,
            'can_combine_with_coupons': True,
            'time_restrictions': {
                'allowed_days': list(range(7))  # All days
            }
        }
        
        result = self.engine.validate_all_rules(self.context, rules)
        self.assertTrue(result.is_valid)
        self.assertEqual(len(result.errors), 0)

    def test_validation_with_errors(self):
        """Test validation that should produce errors."""
        # Create rules that should fail
        rules = {
            'min_booking_value': Decimal('200.00'),  # Too high
            'allowed_categories': [3, 4],  # Not in cart
            'new_users_only': True  # But user might not be new
        }
        
        result = self.engine.validate_all_rules(self.context, rules)
        self.assertFalse(result.is_valid)
        self.assertGreater(len(result.errors), 0)


class AdvancedDiscountEngineTest(TestCase):
    """Test cases for the advanced discount engine."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.engine = AdvancedDiscountEngine()
        
        # Create test items
        self.items = [
            DiscountItem(
                id=1,
                name='Item 1',
                price=Decimal('50.00'),
                category_id=1,
                venue_id=1,
                service_id=1
            ),
            DiscountItem(
                id=2,
                name='Item 2',
                price=Decimal('75.00'),
                category_id=2,
                venue_id=1,
                service_id=2
            )
        ]
        
        # Create test coupons
        self.percentage_coupon = CouponCode.objects.create(
            name='Percentage Coupon',
            coupon_type=CouponType.PERCENTAGE,
            status=CouponStatus.ACTIVE,
            discount_value=Decimal('20.00'),
            valid_from=timezone.now(),
            valid_until=timezone.now() + timedelta(days=30)
        )
        
        self.fixed_coupon = CouponCode.objects.create(
            name='Fixed Coupon',
            coupon_type=CouponType.FIXED_AMOUNT,
            status=CouponStatus.ACTIVE,
            discount_value=Decimal('15.00'),
            valid_from=timezone.now(),
            valid_until=timezone.now() + timedelta(days=30)
        )

    def test_single_discount_validation(self):
        """Test validation of single discount."""
        result = self.engine.validate_discount(
            self.percentage_coupon,
            self.user,
            self.items
        )
        
        self.assertTrue(result.is_valid)
        self.assertEqual(result.discount, self.percentage_coupon)

    def test_discount_calculation(self):
        """Test discount calculation."""
        # Test percentage calculation
        calculation = self.engine.calculate_discount(
            self.percentage_coupon,
            self.items
        )
        
        self.assertIsInstance(calculation, DiscountCalculation)
        self.assertEqual(calculation.discount, self.percentage_coupon)
        self.assertEqual(calculation.original_total, Decimal('125.00'))
        
        # 20% of 125 = 25
        expected_discount = Decimal('25.00')
        self.assertEqual(calculation.discount_amount, expected_discount)
        self.assertEqual(calculation.final_total, Decimal('100.00'))

    def test_multiple_discount_application(self):
        """Test applying multiple discounts."""
        discounts = [self.percentage_coupon, self.fixed_coupon]
        
        result = self.engine.apply_discounts(
            discounts,
            self.user,
            self.items
        )
        
        self.assertTrue(result.is_valid)
        self.assertEqual(len(result.applied_discounts), 2)
        
        # Should apply both discounts
        # First: 20% off 125 = 25 off, leaving 100
        # Second: 15 off 100 = 15 off, leaving 85
        self.assertEqual(result.final_total, Decimal('85.00'))
        self.assertEqual(result.total_savings, Decimal('40.00'))

    def test_discount_priority_ordering(self):
        """Test discount application based on priority."""
        # Set different priorities
        self.percentage_coupon.priority = 20
        self.percentage_coupon.save()
        
        self.fixed_coupon.priority = 10
        self.fixed_coupon.save()
        
        discounts = [self.fixed_coupon, self.percentage_coupon]  # Wrong order
        
        result = self.engine.apply_discounts(
            discounts,
            self.user,
            self.items
        )
        
        # Should apply percentage coupon first (higher priority)
        applied_order = [calc.discount.name for calc in result.applied_discounts]
        self.assertEqual(applied_order[0], 'Percentage Coupon')
        self.assertEqual(applied_order[1], 'Fixed Coupon')

    def test_best_discount_combination(self):
        """Test finding the best discount combination."""
        # Create additional coupon
        high_fixed_coupon = CouponCode.objects.create(
            name='High Fixed Coupon',
            coupon_type=CouponType.FIXED_AMOUNT,
            status=CouponStatus.ACTIVE,
            discount_value=Decimal('30.00'),
            valid_from=timezone.now(),
            valid_until=timezone.now() + timedelta(days=30)
        )
        
        available_discounts = [
            self.percentage_coupon,  # 20% = 25 off
            self.fixed_coupon,       # 15 off
            high_fixed_coupon        # 30 off
        ]
        
        result = self.engine.find_best_discount_combination(
            available_discounts,
            self.user,
            self.items,
            max_combinations=2
        )
        
        # Best should be percentage + high fixed = 25 + 30 = 55 savings
        self.assertGreater(result.total_savings, Decimal('50.00'))

    def test_non_stackable_discount(self):
        """Test non-stackable discount behavior."""
        # Make percentage coupon non-stackable
        self.percentage_coupon.is_stackable = False
        self.percentage_coupon.save()
        
        discounts = [self.percentage_coupon, self.fixed_coupon]
        
        result = self.engine.apply_discounts(
            discounts,
            self.user,
            self.items
        )
        
        # Should only apply one discount
        self.assertEqual(len(result.applied_discounts), 1)
        
        # Should apply the better one (percentage: 25 vs fixed: 15)
        applied_discount = result.applied_discounts[0].discount
        self.assertEqual(applied_discount.name, 'Percentage Coupon')

    def test_discount_with_business_rules(self):
        """Test discount validation with business rules."""
        # Add business rules to coupon
        self.percentage_coupon.business_rules = {
            'min_order_amount': 200.00  # Higher than our total
        }
        self.percentage_coupon.save()
        
        result = self.engine.validate_discount(
            self.percentage_coupon,
            self.user,
            self.items
        )
        
        # Should be invalid due to business rules
        self.assertFalse(result.is_valid)
        self.assertIn('minimum order amount', ' '.join(result.errors).lower())

    def test_usage_recording(self):
        """Test discount usage recording."""
        calculation = self.engine.calculate_discount(
            self.percentage_coupon,
            self.items
        )
        
        # Record usage
        usage = self.engine.record_usage(
            calculation,
            self.user,
            'ORDER123'
        )
        
        self.assertIsNotNone(usage)
        self.assertEqual(usage.user, self.user)
        self.assertEqual(usage.order_reference, 'ORDER123')
        self.assertEqual(usage.discount_amount, calculation.discount_amount)

    def test_empty_items_handling(self):
        """Test handling of empty items list."""
        result = self.engine.apply_discounts(
            [self.percentage_coupon],
            self.user,
            []
        )
        
        self.assertFalse(result.is_valid)
        self.assertIn('no items', ' '.join(result.errors).lower())

    def test_invalid_discount_handling(self):
        """Test handling of invalid discounts."""
        # Create expired coupon
        expired_coupon = CouponCode.objects.create(
            name='Expired Coupon',
            coupon_type=CouponType.PERCENTAGE,
            status=CouponStatus.EXPIRED,
            discount_value=Decimal('10.00'),
            valid_from=timezone.now() - timedelta(days=60),
            valid_until=timezone.now() - timedelta(days=30)
        )
        
        result = self.engine.validate_discount(
            expired_coupon,
            self.user,
            self.items
        )
        
        self.assertFalse(result.is_valid)

    def test_discount_calculation_precision(self):
        """Test discount calculation precision."""
        # Create items with prices that would cause rounding issues
        precision_items = [
            DiscountItem(
                id=1,
                name='Precision Item',
                price=Decimal('33.33'),
                category_id=1,
                venue_id=1,
                service_id=1
            )
        ]
        
        calculation = self.engine.calculate_discount(
            self.percentage_coupon,  # 20%
            precision_items
        )
        
        # 20% of 33.33 = 6.666, should round appropriately
        expected_discount = Decimal('6.67')  # Rounded up
        self.assertEqual(calculation.discount_amount, expected_discount)
        
        expected_final = Decimal('26.66')
        self.assertEqual(calculation.final_total, expected_final) 