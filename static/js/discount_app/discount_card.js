/**
 * Modern Discount Card Component JavaScript
 * 
 * Features:
 * - Real-time countdown timers
 * - Smooth animations and transitions
 * - Intersection Observer for lazy loading
 * - Accessibility enhancements
 * - Performance optimizations
 */

class DiscountCard {
    constructor(element) {
        this.element = element;
        this.countdownElement = element.querySelector('.discount-countdown');
        this.endDate = null;
        this.countdownInterval = null;
        
        this.init();
    }
    
    init() {
        this.setupCountdown();
        this.setupAnimations();
        this.setupAccessibility();
    }
    
    setupCountdown() {
        if (!this.countdownElement) return;
        
        const endDateAttr = this.countdownElement.dataset.endDate;
        if (!endDateAttr) return;
        
        this.endDate = new Date(endDateAttr);
        this.startCountdown();
    }
    
    startCountdown() {
        if (!this.endDate) return;
        
        // Clear any existing interval
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
        }
        
        this.updateCountdown();
        this.countdownInterval = setInterval(() => {
            this.updateCountdown();
        }, 1000);
    }
    
    updateCountdown() {
        const now = new Date().getTime();
        const distance = this.endDate.getTime() - now;
        
        if (distance < 0) {
            this.handleExpiredDiscount();
            return;
        }
        
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        
        this.updateCountdownDisplay(days, hours, minutes);
    }
    
    updateCountdownDisplay(days, hours, minutes) {
        const daysElement = this.countdownElement.querySelector('.days');
        const hoursElement = this.countdownElement.querySelector('.hours');
        const minutesElement = this.countdownElement.querySelector('.minutes');
        
        if (daysElement) daysElement.textContent = days;
        if (hoursElement) hoursElement.textContent = hours;
        if (minutesElement) minutesElement.textContent = minutes;
        
        // Add urgency styling for last 24 hours
        if (days === 0 && hours < 24) {
            this.countdownElement.classList.add('countdown-urgent');
        }
        
        // Add critical styling for last hour
        if (days === 0 && hours === 0 && minutes < 60) {
            this.countdownElement.classList.add('countdown-critical');
        }
    }
    
    handleExpiredDiscount() {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
        
        // Update status to expired
        const statusBadge = this.element.querySelector('.discount-status-badge');
        if (statusBadge) {
            statusBadge.textContent = 'Expired';
            statusBadge.className = 'badge bg-danger discount-status-badge';
        }
        
        // Disable CTA button
        const ctaButton = this.element.querySelector('.discount-cta-btn');
        if (ctaButton) {
            ctaButton.disabled = true;
            ctaButton.textContent = 'Expired';
            ctaButton.classList.add('disabled');
        }
        
        // Hide countdown
        if (this.countdownElement) {
            this.countdownElement.style.display = 'none';
        }
        
        // Add expired styling
        this.element.classList.add('discount-expired');
    }
    
    setupAnimations() {
        // Add entrance animation when card comes into view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });
        
        observer.observe(this.element);
    }
    
    setupAccessibility() {
        // Add ARIA labels for screen readers
        const ctaButton = this.element.querySelector('.discount-cta-btn');
        if (ctaButton) {
            const discountName = this.element.querySelector('.discount-title')?.textContent;
            if (discountName) {
                ctaButton.setAttribute('aria-label', `View details for ${discountName} discount`);
            }
        }
        
        // Add keyboard navigation support
        this.element.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                const ctaButton = this.element.querySelector('.discount-cta-btn');
                if (ctaButton && !ctaButton.disabled) {
                    e.preventDefault();
                    ctaButton.click();
                }
            }
        });
        
        // Make card focusable
        if (!this.element.hasAttribute('tabindex')) {
            this.element.setAttribute('tabindex', '0');
        }
    }
    
    destroy() {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
        }
    }
}

// Auto-initialize discount cards
document.addEventListener('DOMContentLoaded', function() {
    initializeDiscountCards();
});

// Initialize function for dynamic content
function initializeDiscountCards(container = document) {
    const cards = container.querySelectorAll('.discount-card');
    cards.forEach(card => {
        if (!card.discountCardInstance) {
            card.discountCardInstance = new DiscountCard(card);
        }
    });
}

// HTMX integration for dynamic content
document.addEventListener('htmx:afterSwap', function(event) {
    initializeDiscountCards(event.detail.target);
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    const cards = document.querySelectorAll('.discount-card');
    cards.forEach(card => {
        if (card.discountCardInstance) {
            card.discountCardInstance.destroy();
        }
    });
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DiscountCard, initializeDiscountCards };
}
