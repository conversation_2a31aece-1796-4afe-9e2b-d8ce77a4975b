{% load static %}
{% load humanize %}
{% load discount_tags %}

<div class="discount-calculator" id="{{ calculator_id }}">
    <div class="calculator-header">
        <h5 class="calculator-title">
            <i class="fas fa-calculator me-2"></i>Discount Calculator
        </h5>
    </div>

    <div class="calculator-body">
        <!-- Original Price Display -->
        <div class="price-section original-price-section">
            <div class="price-label">Original Price:</div>
            <div class="price-value">{{ currency_symbol }}{{ original_price|floatformat:2 }}</div>
        </div>

        <!-- Discounts Applied -->
        {% if discounts %}
            <div class="discounts-section">
                <div class="section-title">Applied Discounts:</div>
                {% for discount in discounts %}
                    <div class="discount-item">
                        <div class="discount-info">
                            <span class="discount-name">{{ discount.name }}</span>
                            <span class="discount-type badge bg-secondary">
                                {{ discount.discount_type|title }}
                            </span>
                        </div>
                        <div class="discount-value">
                            {% if discount.discount_type == 'PERCENTAGE' %}
                                {{ discount.discount_value|floatformat:0 }}%
                            {% else %}
                                {{ currency_symbol }}{{ discount.discount_value|floatformat:2 }}
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Calculation Results -->
        <div class="results-section">
            <div class="total-savings">
                <div class="savings-label">Total Savings:</div>
                <div class="savings-value text-success">
                    {{ currency_symbol }}{{ calculations.total_savings|floatformat:2 }}
                </div>
            </div>
            
            <div class="final-price">
                <div class="final-price-label">Final Price:</div>
                <div class="final-price-value fw-bold">
                    {{ currency_symbol }}{{ calculations.final_price|floatformat:2 }}
                </div>
            </div>

            {% if calculations.discount_percentage > 0 %}
                <div class="discount-percentage">
                    <div class="percentage-label">Total Discount:</div>
                    <div class="percentage-value text-success">
                        {{ calculations.discount_percentage|floatformat:1 }}%
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Detailed Breakdown (if enabled) -->
        {% if show_breakdown and calculations.breakdown %}
            <div class="breakdown-section">
                <div class="section-title">Detailed Breakdown:</div>
                {% for item in calculations.breakdown %}
                    <div class="breakdown-item">
                        <div class="breakdown-header">
                            <span class="breakdown-name">{{ item.name }}</span>
                            <span class="breakdown-amount text-success">
                                -{{ currency_symbol }}{{ item.amount|floatformat:2 }}
                            </span>
                        </div>
                        <div class="breakdown-details">
                            <small class="text-muted">
                                {{ currency_symbol }}{{ item.price_before|floatformat:2 }} → 
                                {{ currency_symbol }}{{ item.price_after|floatformat:2 }}
                            </small>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    </div>

    <!-- Calculator Footer -->
    <div class="calculator-footer">
        <small class="text-muted">
            <i class="fas fa-info-circle me-1"></i>
            Prices are calculated in real-time based on available discounts
        </small>
    </div>
</div> 