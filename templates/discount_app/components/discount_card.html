{% load static %}
{% load humanize %}
{% load discount_tags %}

<div class="discount-card card h-100 {% if hover_effect %}discount-card-hover{% endif %} discount-card-{{ card_size }}" 
     data-discount-id="{{ discount.id }}" 
     data-discount-type="{{ discount|class_name }}">
     
    <!-- Card Header with Status Badge -->
    <div class="card-header border-0 bg-transparent p-3">
        <div class="d-flex justify-content-between align-items-start">
            <span class="badge bg-{{ status_class }} discount-status-badge">
                {{ status|title }}
            </span>
            {% if discount.discount_type == 'PERCENTAGE' %}
                <div class="discount-value-display">
                    <span class="discount-percentage">{{ discount.discount_value|floatformat:0 }}%</span>
                    <small class="text-muted">OFF</small>
                </div>
            {% else %}
                <div class="discount-value-display">
                    <span class="discount-amount">${{ discount.discount_value|floatformat:2 }}</span>
                    <small class="text-muted">OFF</small>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Card Image -->
    {% if discount.service %}
        <div class="discount-card-image-container">
            <img src="{{ discount.service.venue.get_primary_image|default:'https://via.placeholder.com/400x200' }}" 
                 class="card-img-top discount-card-image" 
                 alt="{{ discount.service.title }}"
                 loading="lazy">
            <div class="discount-overlay">
                <span class="badge bg-primary">Service</span>
            </div>
        </div>
    {% elif discount.venue %}
        <div class="discount-card-image-container">
            <img src="{{ discount.venue.get_primary_image|default:'https://via.placeholder.com/400x200' }}" 
                 class="card-img-top discount-card-image" 
                 alt="{{ discount.venue.name }}"
                 loading="lazy">
            <div class="discount-overlay">
                <span class="badge bg-info">Venue</span>
            </div>
        </div>
    {% else %}
        <div class="discount-card-image-container">
            <div class="discount-placeholder-image d-flex align-items-center justify-content-center">
                <i class="fas fa-tags fa-3x text-muted"></i>
            </div>
            <div class="discount-overlay">
                <span class="badge bg-warning">Platform</span>
            </div>
        </div>
    {% endif %}

    <!-- Card Body -->
    <div class="card-body p-3">
        <!-- Discount Title -->
        <h5 class="card-title discount-title mb-2">{{ discount.name }}</h5>
        
        <!-- Discount Description -->
        {% if discount.description %}
            <p class="card-text discount-description text-muted small mb-3">
                {{ discount.description|truncatewords:15 }}
            </p>
        {% endif %}

        <!-- Service/Venue Information -->
        {% if discount.service %}
            <div class="discount-target-info mb-3">
                <h6 class="mb-1">{{ discount.service.title }}</h6>
                <small class="text-muted">at {{ discount.service.venue.name }}</small>
            </div>
        {% elif discount.venue %}
            <div class="discount-target-info mb-3">
                <h6 class="mb-1">{{ discount.venue.name }}</h6>
                <small class="text-muted">{{ discount.venue.category.name }}</small>
            </div>
        {% endif %}

        <!-- Price Information -->
        {% if original_price and final_price %}
            <div class="discount-pricing mb-3">
                <div class="d-flex align-items-center">
                    <span class="original-price text-decoration-line-through text-muted me-2">
                        ${{ original_price|floatformat:2 }}
                    </span>
                    <span class="final-price fw-bold text-success">
                        ${{ final_price|floatformat:2 }}
                    </span>
                </div>
                <small class="text-success">
                    Save ${{ savings|floatformat:2 }}
                </small>
            </div>
        {% endif %}

        <!-- Progress Bar (if enabled and time-limited) -->
        {% if show_progress and time_remaining %}
            <div class="discount-progress mb-3">
                <div class="d-flex justify-content-between align-items-center mb-1">
                    <small class="text-muted">Time Progress</small>
                    <small class="text-muted">{{ progress_percentage|floatformat:0 }}%</small>
                </div>
                <div class="progress" style="height: 6px;">
                    <div class="progress-bar bg-warning" 
                         role="progressbar" 
                         style="width: {{ progress_percentage }}%"
                         aria-valuenow="{{ progress_percentage }}" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Countdown Timer (if enabled) -->
        {% if show_countdown and time_remaining and time_remaining.total_seconds > 0 %}
            <div class="discount-countdown mb-3" data-end-date="{{ discount.end_date|date:'c' }}">
                <small class="text-muted d-block mb-1">Ends in:</small>
                <div class="countdown-display d-flex gap-2">
                    <div class="countdown-unit">
                        <span class="countdown-value days">0</span>
                        <small class="countdown-label">d</small>
                    </div>
                    <div class="countdown-unit">
                        <span class="countdown-value hours">0</span>
                        <small class="countdown-label">h</small>
                    </div>
                    <div class="countdown-unit">
                        <span class="countdown-value minutes">0</span>
                        <small class="countdown-label">m</small>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Card Footer -->
    <div class="card-footer bg-transparent border-0 p-3 pt-0">
        <div class="d-grid">
            {% if discount.service %}
                <a href="{% url 'discount_app:discount_detail_view' discount.id %}" 
                   class="btn btn-primary discount-cta-btn">
                    View Service Deal
                </a>
            {% elif discount.venue %}
                <a href="{% url 'discount_app:discount_detail_view' discount.id %}" 
                   class="btn btn-primary discount-cta-btn">
                    View Venue Deal
                </a>
            {% else %}
                <a href="{% url 'discount_app:discount_detail_view' discount.id %}" 
                   class="btn btn-primary discount-cta-btn">
                    View Deal
                </a>
            {% endif %}
        </div>
        
        <!-- Additional Info -->
        <div class="discount-meta mt-2 text-center">
            <small class="text-muted">
                Valid until {{ discount.end_date|date:"M d, Y" }}
            </small>
        </div>
    </div>
</div>
