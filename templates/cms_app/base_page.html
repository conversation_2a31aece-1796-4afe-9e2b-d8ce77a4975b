{% extends 'base.html' %}
{% load static wagtailcore_tags wagtailimages_tags %}

{% block title %}{{ page.title }} - CozyWish{% endblock %}

{% block extra_css %}
<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish CMS Pages - Professional Design System */
    
    /* CSS Variables for fonts */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-display: 'Playfair Display', Georgia, serif;
        --brand-primary: #42241A;
        --brand-secondary: #FFF9F4;
        --brand-accent: #D4A574;
    }

    /* Page Header */
    .page-header {
        background: linear-gradient(135deg, var(--brand-secondary) 0%, #F5E6D3 100%);
        padding: 80px 0 60px;
        position: relative;
    }

    .page-header h1 {
        font-family: var(--font-display);
        font-size: 3rem;
        font-weight: 600;
        color: var(--brand-primary);
        margin-bottom: 1rem;
        text-align: center;
    }

    .page-header .intro {
        font-family: var(--font-primary);
        font-size: 1.2rem;
        color: #6B4E3D;
        text-align: center;
        max-width: 800px;
        margin: 0 auto;
        line-height: 1.6;
    }

    /* Content Section */
    .page-content {
        padding: 80px 0;
        background: white;
    }

    .page-content h2 {
        font-family: var(--font-heading);
        font-size: 2rem;
        font-weight: 600;
        color: var(--brand-primary);
        margin-bottom: 1.5rem;
        margin-top: 2.5rem;
    }

    .page-content h2:first-child {
        margin-top: 0;
    }

    .page-content h3 {
        font-family: var(--font-heading);
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--brand-primary);
        margin-bottom: 1rem;
        margin-top: 2rem;
    }

    .page-content p {
        font-family: var(--font-primary);
        font-size: 1.1rem;
        color: #444;
        line-height: 1.7;
        margin-bottom: 1.5rem;
    }

    .page-content ul, .page-content ol {
        font-family: var(--font-primary);
        font-size: 1.1rem;
        color: #444;
        line-height: 1.7;
        margin-bottom: 1.5rem;
        padding-left: 2rem;
    }

    .page-content li {
        margin-bottom: 0.5rem;
    }

    .page-content blockquote {
        background: var(--brand-secondary);
        border-left: 4px solid var(--brand-primary);
        padding: 1.5rem 2rem;
        margin: 2rem 0;
        font-style: italic;
        font-size: 1.2rem;
        color: #555;
    }

    .page-content img {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        margin: 2rem 0;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    /* Call to Action Block */
    .cta-block {
        background: var(--brand-secondary);
        padding: 3rem 2rem;
        border-radius: 16px;
        text-align: center;
        margin: 3rem 0;
    }

    .cta-block h3 {
        font-family: var(--font-heading);
        font-size: 1.8rem;
        font-weight: 600;
        color: var(--brand-primary);
        margin-bottom: 1rem;
    }

    .cta-block p {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 2rem;
    }

    .cta-button {
        background: var(--brand-primary);
        color: white;
        padding: 14px 28px;
        border-radius: 8px;
        font-family: var(--font-heading);
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        border: 2px solid var(--brand-primary);
    }

    .cta-button:hover {
        background: white;
        color: var(--brand-primary);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(66, 36, 26, 0.2);
    }

    .cta-button.secondary {
        background: white;
        color: var(--brand-primary);
        border: 2px solid var(--brand-primary);
    }

    .cta-button.secondary:hover {
        background: var(--brand-primary);
        color: white;
    }

    /* Two Column Layout */
    .two-columns {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
        margin: 2rem 0;
    }

    .three-columns {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 2rem;
        margin: 2rem 0;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .page-header h1 {
            font-size: 2.2rem;
        }
        
        .page-header .intro {
            font-size: 1.1rem;
        }
        
        .page-content h2 {
            font-size: 1.7rem;
        }
        
        .page-content h3 {
            font-size: 1.3rem;
        }
        
        .page-content p,
        .page-content ul,
        .page-content ol {
            font-size: 1rem;
        }
        
        .two-columns,
        .three-columns {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
        
        .cta-block {
            padding: 2rem 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="row">
            <div class="col-lg-10 mx-auto">
                <h1>{{ page.title }}</h1>
                {% if page.intro %}
                    <p class="intro">{{ page.intro }}</p>
                {% endif %}
            </div>
        </div>
    </div>
</section>

<!-- Page Content -->
<section class="page-content">
    <div class="container">
        <div class="row">
            <div class="col-lg-10 mx-auto">
                {% if page.body %}
                    {% for block in page.body %}
                        {% include_block block %}
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
</section>
{% endblock %}
