{% extends 'base.html' %}
{% load static wagtailcore_tags wagtailimages_tags %}

{% block title %}{{ page.title }} - CozyWish{% endblock %}

{% block extra_css %}
<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Home Page - Professional Design System */
    
    /* CSS Variables for fonts */
    :root {
        --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        --font-display: 'Playfair Display', Georgia, serif;
        --brand-primary: #42241A;
        --brand-secondary: #FFF9F4;
        --brand-accent: #D4A574;
    }

    /* Hero Section */
    .hero-section {
        background: linear-gradient(135deg, var(--brand-secondary) 0%, #F5E6D3 100%);
        padding: 120px 0 80px;
        position: relative;
        overflow: hidden;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23000" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23000" opacity="0.02"/><circle cx="50" cy="10" r="0.5" fill="%23000" opacity="0.01"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .hero-title {
        font-family: var(--font-display);
        font-size: 3.5rem;
        font-weight: 600;
        color: var(--brand-primary);
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-family: var(--font-primary);
        font-size: 1.25rem;
        color: #6B4E3D;
        margin-bottom: 2.5rem;
        line-height: 1.6;
    }

    .hero-cta {
        background: var(--brand-primary);
        color: white;
        padding: 16px 32px;
        border-radius: 12px;
        font-family: var(--font-heading);
        font-weight: 600;
        font-size: 1.1rem;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        border: 2px solid var(--brand-primary);
    }

    .hero-cta:hover {
        background: white;
        color: var(--brand-primary);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(66, 36, 26, 0.2);
    }

    /* Content Section */
    .content-section {
        padding: 80px 0;
        background: white;
    }

    .content-section h2 {
        font-family: var(--font-heading);
        font-size: 2.5rem;
        font-weight: 600;
        color: var(--brand-primary);
        margin-bottom: 2rem;
        text-align: center;
    }

    .content-section .intro {
        font-family: var(--font-primary);
        font-size: 1.2rem;
        color: #666;
        text-align: center;
        margin-bottom: 3rem;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Featured Venues Section */
    .featured-venues {
        padding: 80px 0;
        background: var(--brand-secondary);
    }

    .featured-venues h2 {
        font-family: var(--font-heading);
        font-size: 2.5rem;
        font-weight: 600;
        color: var(--brand-primary);
        margin-bottom: 3rem;
        text-align: center;
    }

    .venue-card {
        background: white;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        margin-bottom: 2rem;
    }

    .venue-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    }

    .venue-card img {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }

    .venue-card-body {
        padding: 1.5rem;
    }

    .venue-card h3 {
        font-family: var(--font-heading);
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--brand-primary);
        margin-bottom: 0.5rem;
    }

    .venue-card p {
        font-family: var(--font-primary);
        color: #666;
        font-size: 0.95rem;
        line-height: 1.5;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }
        
        .hero-subtitle {
            font-size: 1.1rem;
        }
        
        .content-section h2,
        .featured-venues h2 {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="hero-title">{{ page.hero_title }}</h1>
                <p class="hero-subtitle">{{ page.hero_subtitle }}</p>
                {% if page.hero_cta_text and page.hero_cta_url %}
                    <a href="{{ page.hero_cta_url }}" class="hero-cta">{{ page.hero_cta_text }}</a>
                {% endif %}
            </div>
        </div>
    </div>
</section>

<!-- Content Section -->
{% if page.intro or page.body %}
<section class="content-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-10 mx-auto">
                {% if page.intro %}
                    <p class="intro">{{ page.intro }}</p>
                {% endif %}
                
                {% if page.body %}
                    <div class="content">
                        {% for block in page.body %}
                            {% include_block block %}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</section>
{% endif %}

<!-- Featured Venues Section -->
{% if page.featured_venues.all %}
<section class="featured-venues">
    <div class="container">
        <h2>Featured Venues</h2>
        <div class="row">
            {% for featured in page.featured_venues.all %}
                <div class="col-lg-4 col-md-6">
                    <div class="venue-card">
                        {% if featured.venue.get_primary_image %}
                            <img src="{{ featured.venue.get_primary_image }}" alt="{{ featured.venue.name }}">
                        {% else %}
                            <img src="https://via.placeholder.com/400x200?text=No+Image" alt="{{ featured.venue.name }}">
                        {% endif %}
                        <div class="venue-card-body">
                            <h3>{{ featured.venue.name }}</h3>
                            {% if featured.description %}
                                <p>{{ featured.description }}</p>
                            {% else %}
                                <p>{{ featured.venue.description|truncatewords:20 }}</p>
                            {% endif %}
                            <a href="{% url 'venues_app:venue_detail' venue_slug=featured.venue.slug %}" class="btn btn-outline-primary btn-sm">View Details</a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}
{% endblock %}
